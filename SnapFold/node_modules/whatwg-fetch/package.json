{"_from": "whatwg-fetch@^3.0.0", "_id": "whatwg-fetch@3.6.20", "_inBundle": false, "_integrity": "sha512-EqhiFU6daOA8kpjOWTL0olhVOF3i7OrFzSYiGsEMB8GcXS+RrzauAERX65xMeNWVqxA6HXH2m69Z9LaKKdisfg==", "_location": "/whatwg-fetch", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "whatwg-fetch@^3.0.0", "name": "whatwg-fetch", "escapedName": "whatwg-fetch", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/react-native"], "_resolved": "https://registry.npmjs.org/whatwg-fetch/-/whatwg-fetch-3.6.20.tgz", "_shasum": "580ce6d791facec91d37c72890995a0b48d31c70", "_spec": "whatwg-fetch@^3.0.0", "_where": "/Users/<USER>/projects/SnapFold/SnapFold/node_modules/react-native", "bugs": {"url": "https://github.com/github/fetch/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A window.fetch polyfill.", "devDependencies": {"abortcontroller-polyfill": "^1.1.9", "auto-changelog": "^2.4.0", "chai": "^4.1.2", "eslint": "^7.20.0", "karma": "^3.0.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-detect-browsers": "^2.3.2", "karma-firefox-launcher": "^1.1.0", "karma-mocha": "^1.3.0", "karma-safari-launcher": "^1.0.0", "karma-safaritechpreview-launcher": "0.0.6", "mocha": "^4.0.1", "prettier": "^1.19.1", "promise-polyfill": "6.0.2", "rollup": "^0.59.1", "url-search-params": "0.6.1"}, "files": ["LICENSE", "dist/fetch.umd.js", "dist/fetch.umd.js.flow", "fetch.js", "fetch.js.flow"], "homepage": "https://github.com/github/fetch#readme", "license": "MIT", "main": "./dist/fetch.umd.js", "module": "./fetch.js", "name": "whatwg-fetch", "repository": {"type": "git", "url": "git+https://github.com/github/fetch.git"}, "scripts": {"karma": "karma start ./test/karma.config.js --no-single-run --auto-watch", "prepare": "make dist/fetch.umd.js dist/fetch.umd.js.flow", "pretest": "make", "test": "karma start ./test/karma.config.js && karma start ./test/karma-worker.config.js", "version": "auto-changelog -p && git add CHANGELOG.md"}, "version": "3.6.20"}