{"_from": "wonka@^6.3.2", "_id": "wonka@6.3.5", "_inBundle": false, "_integrity": "sha512-SSil+ecw6B4/Dm7Pf2sAshKQ5hWFvfyGlfPbEd6A14dOH6VDjrmbY86u6nZvy9omGwwIPFR8V41+of1EezgoUw==", "_location": "/wonka", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "wonka@^6.3.2", "name": "wonka", "escapedName": "wonka", "rawSpec": "^6.3.2", "saveSpec": null, "fetchSpec": "^6.3.2"}, "_requiredBy": ["/@urql/core", "/@urql/exchange-retry"], "_resolved": "https://registry.npmjs.org/wonka/-/wonka-6.3.5.tgz", "_shasum": "33fa54ea700ff3e87b56fe32202112a9e8fea1a2", "_spec": "wonka@^6.3.2", "_where": "/Users/<USER>/projects/SnapFold/SnapFold/node_modules/@urql/core", "author": {"name": "0no.co", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/0no-co/wonka/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A tiny but capable push & pull stream library for TypeScript and Flow", "devDependencies": {"@changesets/cli": "^2.27.1", "@changesets/get-github-info": "^0.6.0", "@rollup/plugin-buble": "^1.0.1", "@rollup/plugin-commonjs": "^23.0.3", "@rollup/plugin-node-resolve": "^15.0.1", "@rollup/plugin-sucrase": "^5.0.1", "@rollup/plugin-terser": "^0.1.0", "@rollup/pluginutils": "^5.0.2", "@types/zen-observable": "^0.8.3", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "callbag-from-iter": "^1.3.0", "callbag-iterate": "^1.0.0", "callbag-take": "^1.5.0", "dotenv": "^16.0.3", "eslint": "^8.29.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-tsdoc": "^0.2.17", "flowgen": "^1.21.0", "glob": "^8.0.3", "husky-v4": "^4.3.8", "lint-staged": "^13.0.4", "npm-run-all": "^4.1.5", "prettier": "^2.8.0", "rimraf": "^3.0.2", "rollup": "^3.5.1", "rollup-plugin-cjs-check": "^1.0.1", "rollup-plugin-dts": "^5.1.1", "tslib": "^2.4.1", "typescript": "^4.9.5", "vitest": "^1.2.2", "zen-observable": "^0.10.0"}, "eslintConfig": {"root": true, "extends": ["./scripts/eslint-preset.js"]}, "exports": {".": {"types": "./dist/wonka.d.ts", "import": "./dist/wonka.mjs", "require": "./dist/wonka.js", "source": "./src/index.ts"}, "./package.json": "./package.json"}, "files": ["src", "dist", "docs/*.md", "index.js.flow", "*.md"], "homepage": "https://github.com/0no-co/wonka#readme", "husky": {"hooks": {"pre-commit": "lint-staged --quiet --relative"}}, "keywords": ["wonka", "typescript", "events", "callbag", "callback", "observable", "iterable", "stream"], "license": "MIT", "lint-staged": {"*.{ts,js}": "eslint -c scripts/eslint-preset.js --fix", "*.json": "prettier --write", "*.md": "prettier --write"}, "main": "./dist/wonka", "module": "./dist/wonka.mjs", "name": "wonka", "prettier": {"singleQuote": true, "tabWidth": 2, "printWidth": 100}, "publishConfig": {"provenance": true}, "repository": {"type": "git", "url": "git+https://github.com/0no-co/wonka.git"}, "scripts": {"build": "rollup -c scripts/rollup.config.mjs", "changeset:publish": "changeset publish", "changeset:version": "changeset version && pnpm install --lockfile-only", "check": "tsc", "clean": "rimraf dist node_modules/.cache", "lint": "eslint --ext=js,ts .", "test": "vitest run"}, "sideEffects": false, "source": "./src/index.ts", "types": "./dist/wonka.d.ts", "version": "6.3.5"}