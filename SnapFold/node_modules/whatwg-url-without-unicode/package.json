{"_from": "whatwg-url-without-unicode@8.0.0-3", "_id": "whatwg-url-without-unicode@8.0.0-3", "_inBundle": false, "_integrity": "sha512-HoKuzZrUlgpz35YO27XgD28uh/WJH4B0+3ttFqRo//lmq+9T/mIOJ6kqmINI9HpUpz1imRC/nR/lxKpJiv0uig==", "_location": "/whatwg-url-without-unicode", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "whatwg-url-without-unicode@8.0.0-3", "name": "whatwg-url-without-unicode", "escapedName": "whatwg-url-without-unicode", "rawSpec": "8.0.0-3", "saveSpec": null, "fetchSpec": "8.0.0-3"}, "_requiredBy": ["/expo"], "_resolved": "https://registry.npmjs.org/whatwg-url-without-unicode/-/whatwg-url-without-unicode-8.0.0-3.tgz", "_shasum": "ab6df4bf6caaa6c85a59f6e82c026151d4bb376b", "_spec": "whatwg-url-without-unicode@8.0.0-3", "_where": "/Users/<USER>/projects/SnapFold/SnapFold/node_modules/expo", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/charpeni/whatwg-url/issues"}, "bundleDependencies": false, "dependencies": {"buffer": "^5.4.3", "punycode": "^2.1.1", "webidl-conversions": "^5.0.0"}, "deprecated": false, "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery, without unicode support", "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.8.7", "@babel/plugin-transform-new-target": "^7.8.3", "browserify": "^16.5.0", "domexception": "^2.0.1", "eslint": "^6.8.0", "got": "^10.2.1", "jest": "^24.9.0", "recast": "^0.18.5", "webidl2js": "^12.0.0"}, "engines": {"node": ">=10"}, "files": ["index.js", "webidl2js-wrapper.js", "lib/"], "homepage": "https://github.com/charpeni/whatwg-url#readme", "jest": {"collectCoverageFrom": ["lib/**/*.js", "!lib/utils.js"], "coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "testEnvironment": "node", "testMatch": ["<rootDir>/test/**/*.js"], "testPathIgnorePatterns": ["^<rootDir>/test/testharness.js$", "^<rootDir>/test/web-platform-tests/"]}, "license": "MIT", "main": "index.js", "name": "whatwg-url-without-unicode", "repository": {"type": "git", "url": "git+https://github.com/charpeni/whatwg-url.git"}, "scripts": {"build": "node scripts/transform.js && node scripts/convert-idl.js && babel lib/URL.js lib/URLSearchParams.js -d lib", "build-live-viewer": "browserify index.js --standalone whatwgURL > live-viewer/whatwg-url.js", "coverage": "jest --coverage", "lint": "eslint .", "prepare": "npm run build", "pretest": "node scripts/get-latest-platform-tests.js && npm run build", "test": "jest"}, "version": "8.0.0-3"}