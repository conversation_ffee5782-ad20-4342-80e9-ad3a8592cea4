const set = require('regenerate')(0x23, 0x2A, 0xA9, 0xAE, 0x203C, 0x2049, 0x2122, 0x2139, 0x2328, 0x23CF, 0x24C2, 0x25B6, 0x25C0, 0x260E, 0x2611, 0x2618, 0x261D, 0x2620, 0x2626, 0x262A, 0x2640, 0x2642, 0x2663, 0x2668, 0x267B, 0x2699, 0x26A7, 0x26C8, 0x26D1, 0x26FD, 0x2702, 0x2705, 0x270F, 0x2712, 0x2714, 0x2716, 0x271D, 0x2721, 0x2728, 0x2744, 0x2747, 0x274C, 0x274E, 0x2757, 0x27A1, 0x27B0, 0x27BF, 0x2B50, 0x2B55, 0x3030, 0x303D, 0x3297, 0x3299, 0x1F004, 0x1F0CF, 0x1F18E, 0x1F21A, 0x1F22F, 0x1F587, 0x1F590, 0x1F5A8, 0x1F5BC, 0x1F5E1, 0x1F5E3, 0x1F5E8, 0x1F5EF, 0x1F5F3, 0x1F6E9, 0x1F6F0, 0x1F7F0);
set.addRange(0x30, 0x39).addRange(0x2194, 0x2199).addRange(0x21A9, 0x21AA).addRange(0x231A, 0x231B).addRange(0x23E9, 0x23F3).addRange(0x23F8, 0x23FA).addRange(0x25AA, 0x25AB).addRange(0x25FB, 0x25FE).addRange(0x2600, 0x2604).addRange(0x2614, 0x2615).addRange(0x2622, 0x2623).addRange(0x262E, 0x262F).addRange(0x2638, 0x263A).addRange(0x2648, 0x2653).addRange(0x265F, 0x2660).addRange(0x2665, 0x2666).addRange(0x267E, 0x267F).addRange(0x2692, 0x2697).addRange(0x269B, 0x269C).addRange(0x26A0, 0x26A1).addRange(0x26AA, 0x26AB).addRange(0x26B0, 0x26B1).addRange(0x26BD, 0x26BE).addRange(0x26C4, 0x26C5).addRange(0x26CE, 0x26CF).addRange(0x26D3, 0x26D4).addRange(0x26E9, 0x26EA).addRange(0x26F0, 0x26F5).addRange(0x26F7, 0x26FA).addRange(0x2708, 0x270D).addRange(0x2733, 0x2734).addRange(0x2753, 0x2755).addRange(0x2763, 0x2764).addRange(0x2795, 0x2797).addRange(0x2934, 0x2935).addRange(0x2B05, 0x2B07).addRange(0x2B1B, 0x2B1C).addRange(0x1F170, 0x1F171).addRange(0x1F17E, 0x1F17F).addRange(0x1F191, 0x1F19A).addRange(0x1F1E6, 0x1F1FF).addRange(0x1F201, 0x1F202).addRange(0x1F232, 0x1F23A).addRange(0x1F250, 0x1F251).addRange(0x1F300, 0x1F321).addRange(0x1F324, 0x1F393).addRange(0x1F396, 0x1F397).addRange(0x1F399, 0x1F39B).addRange(0x1F39E, 0x1F3F0).addRange(0x1F3F3, 0x1F3F5).addRange(0x1F3F7, 0x1F4FD);
set.addRange(0x1F4FF, 0x1F53D).addRange(0x1F549, 0x1F54E).addRange(0x1F550, 0x1F567).addRange(0x1F56F, 0x1F570).addRange(0x1F573, 0x1F57A).addRange(0x1F58A, 0x1F58D).addRange(0x1F595, 0x1F596).addRange(0x1F5A4, 0x1F5A5).addRange(0x1F5B1, 0x1F5B2).addRange(0x1F5C2, 0x1F5C4).addRange(0x1F5D1, 0x1F5D3).addRange(0x1F5DC, 0x1F5DE).addRange(0x1F5FA, 0x1F64F).addRange(0x1F680, 0x1F6C5).addRange(0x1F6CB, 0x1F6D2).addRange(0x1F6D5, 0x1F6D7).addRange(0x1F6DC, 0x1F6E5).addRange(0x1F6EB, 0x1F6EC).addRange(0x1F6F3, 0x1F6FC).addRange(0x1F7E0, 0x1F7EB).addRange(0x1F90C, 0x1F93A).addRange(0x1F93C, 0x1F945).addRange(0x1F947, 0x1F9FF).addRange(0x1FA70, 0x1FA7C).addRange(0x1FA80, 0x1FA89).addRange(0x1FA8F, 0x1FAC6).addRange(0x1FACE, 0x1FADC).addRange(0x1FADF, 0x1FAE9).addRange(0x1FAF0, 0x1FAF8);
exports.characters = set;
