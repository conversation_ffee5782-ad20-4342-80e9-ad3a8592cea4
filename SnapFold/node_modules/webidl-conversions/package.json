{"_from": "webidl-conversions@^5.0.0", "_id": "webidl-conversions@5.0.0", "_inBundle": false, "_integrity": "sha512-VlZwKPCkYKxQgeSbH5EyngOmRp7Ww7I9rQLERETtf5ofd9pGeswWiOtogpEO850jziPRarreGxn5QIiTqpb2wA==", "_location": "/webidl-conversions", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "webidl-conversions@^5.0.0", "name": "webidl-conversions", "escapedName": "webidl-conversions", "rawSpec": "^5.0.0", "saveSpec": null, "fetchSpec": "^5.0.0"}, "_requiredBy": ["/whatwg-url-without-unicode"], "_resolved": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-5.0.0.tgz", "_shasum": "ae59c8a00b121543a2acc65c0434f57b0fc11aff", "_spec": "webidl-conversions@^5.0.0", "_where": "/Users/<USER>/projects/SnapFold/SnapFold/node_modules/whatwg-url-without-unicode", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "bugs": {"url": "https://github.com/jsdom/webidl-conversions/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Implements the WebIDL algorithms for converting to and from JavaScript values", "devDependencies": {"eslint": "^6.7.2", "mocha": "^6.2.2", "nyc": "^14.1.1"}, "engines": {"node": ">=8"}, "files": ["lib/"], "homepage": "https://github.com/jsdom/webidl-conversions#readme", "keywords": ["webidl", "web", "types"], "license": "BSD-2-<PERSON><PERSON>", "main": "lib/index.js", "name": "webidl-conversions", "repository": {"type": "git", "url": "git+https://github.com/jsdom/webidl-conversions.git"}, "scripts": {"coverage": "nyc mocha test/*.js", "lint": "eslint .", "test": "mocha test/*.js"}, "version": "5.0.0"}