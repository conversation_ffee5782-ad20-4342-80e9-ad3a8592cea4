{"_from": "which@^2.0.1", "_id": "which@2.0.2", "_inBundle": false, "_integrity": "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==", "_location": "/which", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "which@^2.0.1", "name": "which", "escapedName": "which", "rawSpec": "^2.0.1", "saveSpec": null, "fetchSpec": "^2.0.1"}, "_requiredBy": ["/cross-spawn"], "_resolved": "https://registry.npmjs.org/which/-/which-2.0.2.tgz", "_shasum": "7c6a8dd0a636a0327e10b59c9286eee93f3f51b1", "_spec": "which@^2.0.1", "_where": "/Users/<USER>/projects/SnapFold/SnapFold/node_modules/cross-spawn", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "bin": {"node-which": "bin/node-which"}, "bugs": {"url": "https://github.com/isaacs/node-which/issues"}, "bundleDependencies": false, "dependencies": {"isexe": "^2.0.0"}, "deprecated": false, "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "devDependencies": {"mkdirp": "^0.5.0", "rimraf": "^2.6.2", "tap": "^14.6.9"}, "engines": {"node": ">= 8"}, "files": ["which.js", "bin/node-which"], "homepage": "https://github.com/isaacs/node-which#readme", "license": "ISC", "main": "which.js", "name": "which", "repository": {"type": "git", "url": "git://github.com/isaacs/node-which.git"}, "scripts": {"changelog": "git add CHANGELOG.md", "postchangelog": "git commit -m 'update changelog - '${npm_package_version}", "postpublish": "git push origin --follow-tags", "postversion": "npm publish", "prechangelog": "bash gen-changelog.sh", "prepublish": "npm run changelog", "preversion": "npm test", "test": "tap"}, "tap": {"check-coverage": true}, "version": "2.0.2"}