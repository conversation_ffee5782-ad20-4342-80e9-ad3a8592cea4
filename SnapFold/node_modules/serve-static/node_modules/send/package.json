{"_from": "send@0.19.0", "_id": "send@0.19.0", "_inBundle": false, "_integrity": "sha512-dW41u5VfLXu8SJh5bwRmyYUbAoSB3c9uQh6L8h/KtsFREPWpbX1lrljJo186Jc4nmci/sGUZ9a0a0J2zgfq2hw==", "_location": "/serve-static/send", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "send@0.19.0", "name": "send", "escapedName": "send", "rawSpec": "0.19.0", "saveSpec": null, "fetchSpec": "0.19.0"}, "_requiredBy": ["/serve-static"], "_resolved": "https://registry.npmjs.org/send/-/send-0.19.0.tgz", "_shasum": "bbc5a388c8ea6c048967049dbeac0e4a3f09d7f8", "_spec": "send@0.19.0", "_where": "/Users/<USER>/projects/SnapFold/SnapFold/node_modules/serve-static", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/pillarjs/send/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {"debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "2.0.0", "mime": "1.6.0", "ms": "2.1.3", "on-finished": "2.4.1", "range-parser": "~1.2.1", "statuses": "2.0.1"}, "deprecated": false, "description": "Better streaming static file server with Range and conditional-GET support", "devDependencies": {"after": "0.8.2", "eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "9.2.2", "nyc": "15.1.0", "supertest": "6.2.2"}, "engines": {"node": ">= 0.8.0"}, "files": ["HISTORY.md", "LICENSE", "README.md", "SECURITY.md", "index.js"], "homepage": "https://github.com/pillarjs/send#readme", "keywords": ["static", "file", "server"], "license": "MIT", "name": "send", "repository": {"type": "git", "url": "git+https://github.com/pillarjs/send.git"}, "scripts": {"lint": "eslint .", "test": "mocha --check-leaks --reporter spec --bail", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "version": "0.19.0"}