import { StatusBar } from 'expo-status-bar';
import { StyleSheet, Text, View, TouchableOpacity, SafeAreaView } from 'react-native';

export default function App() {
  // Sample poker scenario data
  const scenario = {
    holeCards: ['A♠', 'K♦'],
    position: 'Button',
    stackSize: 100,
    potSize: 7.5,
    actionHistory: 'UTG raises to 2.5bb, MP calls',
    effectiveStacks: 100
  };

  const handleAction = (action) => {
    console.log(`Player chose: ${action}`);
    // TODO: Handle the action and show consensus data
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />

      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.appTitle}>SnapFold</Text>
        <Text style={styles.subtitle}>Poker Training</Text>
      </View>

      {/* Scenario Info */}
      <View style={styles.scenarioContainer}>
        <Text style={styles.sectionTitle}>Your Position: {scenario.position}</Text>

        {/* Hole Cards */}
        <View style={styles.cardsContainer}>
          <Text style={styles.cardsLabel}>Your Cards:</Text>
          <View style={styles.cardsRow}>
            {scenario.holeCards.map((card, index) => (
              <View key={index} style={styles.card}>
                <Text style={styles.cardText}>{card}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Game Info */}
        <View style={styles.gameInfo}>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Stack Size:</Text>
            <Text style={styles.infoValue}>{scenario.stackSize}bb</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Pot Size:</Text>
            <Text style={styles.infoValue}>{scenario.potSize}bb</Text>
          </View>
        </View>

        {/* Action History */}
        <View style={styles.actionHistory}>
          <Text style={styles.actionLabel}>Action:</Text>
          <Text style={styles.actionText}>{scenario.actionHistory}</Text>
        </View>
      </View>

      {/* Decision Buttons */}
      <View style={styles.buttonsContainer}>
        <Text style={styles.decisionPrompt}>What's your move?</Text>

        <View style={styles.buttonRow}>
          <TouchableOpacity
            style={[styles.actionButton, styles.foldButton]}
            onPress={() => handleAction('FOLD')}
          >
            <Text style={styles.buttonText}>FOLD</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.callButton]}
            onPress={() => handleAction('CALL')}
          >
            <Text style={styles.buttonText}>CALL</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.raiseButton]}
            onPress={() => handleAction('RAISE')}
          >
            <Text style={styles.buttonText}>RAISE</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0f172a', // Dark poker table background
  },
  header: {
    alignItems: 'center',
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#334155',
  },
  appTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#f1f5f9',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#94a3b8',
  },
  scenarioContainer: {
    flex: 1,
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#f1f5f9',
    marginBottom: 20,
    textAlign: 'center',
  },
  cardsContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  cardsLabel: {
    fontSize: 16,
    color: '#94a3b8',
    marginBottom: 10,
  },
  cardsRow: {
    flexDirection: 'row',
    gap: 15,
  },
  card: {
    width: 60,
    height: 85,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  cardText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1e293b',
  },
  gameInfo: {
    backgroundColor: '#1e293b',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  infoLabel: {
    fontSize: 16,
    color: '#94a3b8',
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#f1f5f9',
  },
  actionHistory: {
    backgroundColor: '#1e293b',
    borderRadius: 12,
    padding: 20,
  },
  actionLabel: {
    fontSize: 16,
    color: '#94a3b8',
    marginBottom: 8,
  },
  actionText: {
    fontSize: 16,
    color: '#f1f5f9',
    lineHeight: 22,
  },
  buttonsContainer: {
    padding: 20,
    paddingBottom: 40,
  },
  decisionPrompt: {
    fontSize: 18,
    fontWeight: '600',
    color: '#f1f5f9',
    textAlign: 'center',
    marginBottom: 20,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 10,
  },
  actionButton: {
    flex: 1,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  foldButton: {
    backgroundColor: '#dc2626',
  },
  callButton: {
    backgroundColor: '#2563eb',
  },
  raiseButton: {
    backgroundColor: '#16a34a',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#ffffff',
  },
});
