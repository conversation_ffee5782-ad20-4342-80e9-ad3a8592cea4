{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/components/*": ["src/components/*"], "@/screens/*": ["src/screens/*"], "@/types/*": ["src/types/*"], "@/utils/*": ["src/utils/*"], "@/constants/*": ["src/constants/*"], "@/hooks/*": ["src/hooks/*"], "@/services/*": ["src/services/*"], "@/store/*": ["src/store/*"]}}, "include": ["**/*.ts", "**/*.tsx"]}