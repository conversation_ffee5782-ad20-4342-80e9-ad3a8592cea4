# Hand Scenario JSON Templates

This directory contains JSON templates for poker hand scenarios that can be loaded into the SnapFold app.

## Template Structure

Each hand scenario JSON file contains:

### Required Fields

- **Basic Info**: `id`, `title`, `description`, `difficulty`, `tags`
- **Game Settings**: `gameSettings` (blinds, game type, table size)
- **Players**: `players` array with position, stack, and player info
- **Hero Info**: `heroPosition`, `heroCards`
- **Game State**: `board`, `currentStreet`, `potState`
- **Actions**: `actionHistory`, `decisionPoint`
- **Animation**: `animationSequence` with timing and chat settings

### Optional Fields

- **Educational**: `analysis` with correct action and explanations
- **Metadata**: `createdAt`, `updatedAt`, `author`, `source`

## Example Usage

### Loading a Scenario

```typescript
import { loadHandScenario, convertToGameFormat } from '../utils/handScenarioLoader';

// Load from JSON file
const scenario = await loadHandScenario('./sampleHandScenario.json');

// Convert to game format
const gameData = convertToGameFormat(scenario);

// Use in components
<GameScreen scenario={gameData} />
```

### Creating a New Scenario

```json
{
  "id": "my_custom_hand",
  "title": "Custom Training Hand",
  "description": "A custom scenario for training",
  "difficulty": "INTERMEDIATE",
  "tags": ["custom", "training"],
  
  "gameSettings": {
    "gameType": "CASH",
    "smallBlind": 25,
    "bigBlind": 50,
    "maxPlayers": 6,
    "currentPlayers": 4
  },
  
  "players": [
    {
      "id": "utg",
      "name": "UTG Player",
      "position": "UTG",
      "stackSize": 2500,
      "stackSizeBB": 50,
      "isHero": false,
      "isActive": true,
      "isActing": false,
      "hasCards": true,
      "seatNumber": 1
    },
    {
      "id": "hero",
      "name": "Hero",
      "position": "BB",
      "stackSize": 3000,
      "stackSizeBB": 60,
      "isHero": true,
      "isActive": true,
      "isActing": true,
      "hasCards": true,
      "seatNumber": 6
    }
  ],
  
  "heroPosition": "BB",
  "heroCards": [
    {"rank": "A", "suit": "spades"},
    {"rank": "K", "suit": "hearts"}
  ],
  
  "animationSequence": {
    "street": "PREFLOP",
    "actions": [
      {
        "playerId": "utg",
        "position": "UTG",
        "action": "RAISE",
        "amount": 125,
        "amountBB": 2.5,
        "timestamp": 0,
        "animationDelay": 0,
        "showInChat": true,
        "chatMessage": "UTG raises to 2.5bb"
      }
    ],
    "autoPlay": false,
    "showChat": true,
    "defaultActionDelay": 1000,
    "messageDisplayTime": 2000,
    "fadeInDuration": 600,
    "fadeOutDuration": 600
  }
}
```

## Animation Configuration

### Chat Messages

Control vanishing chat with `animationSequence.actions`:

```json
{
  "playerId": "player_id",
  "position": "UTG",
  "action": "RAISE",
  "amount": 125,
  "amountBB": 2.5,
  "animationDelay": 1000,     // Delay before showing (ms)
  "showInChat": true,         // Show in vanishing chat
  "chatMessage": "UTG raises to 2.5bb"  // Custom message
}
```

### Timing Settings

```json
{
  "animationSequence": {
    "defaultActionDelay": 1000,    // Default delay between actions
    "messageDisplayTime": 2000,    // How long each message shows
    "fadeInDuration": 600,         // Fade in animation time
    "fadeOutDuration": 600,        // Fade out animation time
    "autoPlay": false,             // Auto-start animation
    "showChat": true,              // Enable vanishing chat
    "showOverlay": false           // Enable action overlay
  }
}
```

## Card Format

Cards use rank and suit:

```json
{
  "rank": "A",           // 2-9, T, J, Q, K, A
  "suit": "spades",      // hearts, diamonds, clubs, spades
  "displayRank": "A",    // Optional: custom display
  "displaySuit": "♠"     // Optional: custom suit symbol
}
```

## Player Positions

Valid positions: `UTG`, `UTG+1`, `MP`, `MP+1`, `CO`, `BTN`, `SB`, `BB`

## Action Types

Valid actions: `FOLD`, `CHECK`, `CALL`, `RAISE`, `BET`, `ALL_IN`

## Difficulty Levels

- `BEGINNER`: Basic concepts
- `INTERMEDIATE`: Standard situations
- `ADVANCED`: Complex spots
- `EXPERT`: High-level play

## File Naming Convention

- `hand_[number]_[concept]_[position].json`
- Example: `hand_001_3bet_decision_bb.json`

## Validation

The `handScenarioLoader.ts` utility validates:

- Required fields are present
- Card formats are valid
- Player data is consistent
- Action sequences are logical
- Hero player exists and matches position

## Auto-Generation

Future versions will support auto-generating scenarios from:

- Hand history files
- Training concepts
- Difficulty progression
- Player skill assessment
