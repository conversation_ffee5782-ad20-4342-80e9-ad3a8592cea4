/**
 * Hand Scenario Loader Utility
 * 
 * Utilities for loading, parsing, and validating hand scenario JSON files.
 * Handles conversion from JSON to typed HandScenario objects with validation.
 */

import { HandScenario, Card, Player, PlayerAction, ActionSequence } from '../types/HandScenario';

/**
 * Validates that a card object has the required properties
 */
function validateCard(card: any): card is Card {
  return (
    card &&
    typeof card.rank === 'string' &&
    typeof card.suit === 'string' &&
    ['2', '3', '4', '5', '6', '7', '8', '9', 'T', 'J', 'Q', 'K', 'A'].includes(card.rank) &&
    ['hearts', 'diamonds', 'clubs', 'spades'].includes(card.suit)
  );
}

/**
 * Validates that a player object has the required properties
 */
function validatePlayer(player: any): player is Player {
  const requiredFields = [
    'id', 'name', 'position', 'stackSize', 'stackSizeBB',
    'isHero', 'isActive', 'seatNumber', 'isActing', 'hasCards'
  ];

  for (const field of requiredFields) {
    if (!(field in player)) {
      console.error(`Missing field: ${field}`);
      return false;
    }
  }

  const typeChecks = (
    typeof player.id === 'string' &&
    typeof player.name === 'string' &&
    typeof player.position === 'string' &&
    typeof player.stackSize === 'number' &&
    typeof player.stackSizeBB === 'number' &&
    typeof player.isHero === 'boolean' &&
    typeof player.isActive === 'boolean' &&
    typeof player.seatNumber === 'number' &&
    typeof player.isActing === 'boolean' &&
    typeof player.hasCards === 'boolean'
  );

  if (!typeChecks) {
    console.error('Type validation failed for player:', player);
  }

  return typeChecks;
}

/**
 * Validates that an action object has the required properties
 */
function validateAction(action: any): action is PlayerAction {
  return (
    action &&
    typeof action.playerId === 'string' &&
    typeof action.action === 'string' &&
    ['FOLD', 'CHECK', 'CALL', 'RAISE', 'BET', 'ALL_IN'].includes(action.action) &&
    // position and timestamp are optional, will be set to defaults if missing
    (action.position === undefined || typeof action.position === 'string') &&
    (action.timestamp === undefined || typeof action.timestamp === 'number')
  );
}

/**
 * Converts a raw JSON object to a validated HandScenario
 */
export function parseHandScenario(jsonData: any): HandScenario {
  // Basic validation
  if (!jsonData || typeof jsonData !== 'object') {
    throw new Error('Invalid hand scenario data: must be an object');
  }

  // Validate required fields
  const requiredFields = [
    'id', 'title', 'description', 'difficulty', 'tags',
    'gameSettings', 'players', 'heroPosition', 'heroCards',
    'board', 'currentStreet', 'potState', 'actionHistory',
    'decisionPoint', 'animationSequence'
  ];

  for (const field of requiredFields) {
    if (!(field in jsonData)) {
      throw new Error(`Missing required field: ${field}`);
    }
  }

  // Validate hero cards
  if (!Array.isArray(jsonData.heroCards) || jsonData.heroCards.length !== 2) {
    throw new Error('Hero cards must be an array of exactly 2 cards');
  }

  for (const card of jsonData.heroCards) {
    if (!validateCard(card)) {
      throw new Error(`Invalid hero card: ${JSON.stringify(card)}`);
    }
  }

  // Validate players
  if (!Array.isArray(jsonData.players) || jsonData.players.length === 0) {
    throw new Error('Players must be a non-empty array');
  }

  // Validate and normalize players
  for (const player of jsonData.players) {
    // Set default values for optional fields before validation
    if (player.isActing === undefined) {
      player.isActing = player.isHero && player.isActive;
    }
    if (player.hasCards === undefined) {
      player.hasCards = player.isActive;
    }

    if (!validatePlayer(player)) {
      console.error('Player validation failed for:', player);
      throw new Error(`Invalid player: ${JSON.stringify(player)}`);
    }
  }

  // Find hero player
  const heroPlayer = jsonData.players.find((p: any) => p.isHero);
  if (!heroPlayer) {
    throw new Error('No hero player found in players array');
  }

  if (heroPlayer.position !== jsonData.heroPosition) {
    throw new Error('Hero position mismatch between heroPosition and hero player');
  }

  // Validate action history
  if (!jsonData.actionHistory || typeof jsonData.actionHistory !== 'object') {
    throw new Error('Action history must be an object');
  }

  if (!Array.isArray(jsonData.actionHistory.preflop)) {
    throw new Error('Preflop action history must be an array');
  }

  // Validate and normalize preflop actions
  for (const action of jsonData.actionHistory.preflop) {
    if (!validateAction(action)) {
      throw new Error(`Invalid preflop action: ${JSON.stringify(action)}`);
    }

    // Set default values for optional fields
    if (action.position === undefined) {
      // Try to find position from player ID
      const player = jsonData.players.find((p: any) => p.id === action.playerId);
      if (player) {
        action.position = player.position;
      }
    }
    if (action.timestamp === undefined) {
      action.timestamp = 0;
    }
  }

  // Convert dates
  const createdAt = jsonData.createdAt ? new Date(jsonData.createdAt) : new Date();
  const updatedAt = jsonData.updatedAt ? new Date(jsonData.updatedAt) : new Date();

  // Return validated and typed HandScenario
  return {
    ...jsonData,
    createdAt,
    updatedAt,
  } as HandScenario;
}

/**
 * Loads a hand scenario from a JSON file
 */
export async function loadHandScenario(filePath: string): Promise<HandScenario> {
  try {
    // In a real app, this would fetch from a file or API
    // For now, we'll simulate loading the sample scenario
    const response = await fetch(filePath);
    if (!response.ok) {
      throw new Error(`Failed to load hand scenario: ${response.statusText}`);
    }
    
    const jsonData = await response.json();
    return parseHandScenario(jsonData);
  } catch (error) {
    throw new Error(`Error loading hand scenario from ${filePath}: ${error}`);
  }
}

/**
 * Converts a HandScenario to the format expected by the game components
 */
export function convertToGameFormat(scenario: HandScenario) {
  // Convert to the format expected by existing components
  return {
    // Basic scenario info
    id: scenario.id,
    title: scenario.title,
    description: scenario.description,
    
    // Game state
    holeCards: scenario.heroCards.map(card => `${card.rank}${card.suit[0]}`),
    position: scenario.heroPosition,
    potSize: scenario.potState.totalPotBB,
    effectiveStacks: Math.min(...scenario.players.filter(p => p.isActive).map(p => p.stackSizeBB)),
    actionHistory: scenario.actionHistory.preflop.map(action => 
      `${action.position} ${action.action.toLowerCase()}${action.amount ? ` ${action.amountBB}bb` : ''}`
    ).join(', '),
    
    // Animation data
    chatMessages: scenario.animationSequence.actions
      .filter(action => action.showInChat)
      .map((action, index) => ({
        id: `${index + 1}`,
        position: action.position,
        action: action.action,
        amount: action.amountBB,
        delay: action.animationDelay || 0,
      })),
    
    // Player data
    players: scenario.players.map(player => ({
      position: player.position,
      stack: player.stackSize,
      stackBB: player.stackSizeBB,
      isActive: player.isActive,
      isHero: player.isHero,
      isActing: player.isActing,
    })),
    
    // Game settings
    bigBlind: scenario.gameSettings.bigBlind,
    smallBlind: scenario.gameSettings.smallBlind,
    
    // Educational content
    tags: scenario.tags,
    difficulty: scenario.difficulty,
    concept: scenario.concept,
    analysis: scenario.analysis,
    
    // Metadata
    createdAt: scenario.createdAt,
  };
}

/**
 * Creates a minimal hand scenario template for quick testing
 */
export function createTestScenario(): HandScenario {
  return parseHandScenario({
    id: 'test_scenario',
    title: 'Test Scenario',
    description: 'A simple test scenario',
    difficulty: 'BEGINNER',
    tags: ['test'],
    
    gameSettings: {
      gameType: 'CASH',
      smallBlind: 25,
      bigBlind: 50,
      maxPlayers: 6,
      currentPlayers: 3,
    },
    
    players: [
      {
        id: 'utg',
        name: 'UTG',
        position: 'UTG',
        stackSize: 2500,
        stackSizeBB: 50,
        isHero: false,
        isActive: true,
        isActing: false,
        hasCards: true,
        seatNumber: 1,
      },
      {
        id: 'hero',
        name: 'Hero',
        position: 'BB',
        stackSize: 3000,
        stackSizeBB: 60,
        isHero: true,
        isActive: true,
        isActing: true,
        hasCards: true,
        seatNumber: 6,
      },
    ],
    
    heroPosition: 'BB',
    heroCards: [
      { rank: 'A', suit: 'spades' },
      { rank: 'K', suit: 'hearts' },
    ],
    
    board: {
      allCards: [],
      street: 'PREFLOP',
    },
    
    currentStreet: 'PREFLOP',
    
    potState: {
      mainPot: 75,
      mainPotBB: 1.5,
      totalPot: 75,
      totalPotBB: 1.5,
    },
    
    currentBet: 0,
    currentBetBB: 0,
    
    actionHistory: {
      preflop: [],
    },
    
    decisionPoint: {
      facingAction: null,
      legalActions: ['CHECK', 'BET'],
      difficulty: 'BEGINNER',
      tags: ['test'],
      previousActions: [],
    },
    
    animationSequence: {
      street: 'PREFLOP',
      actions: [],
      autoPlay: false,
      showChat: false,
      showOverlay: false,
      defaultActionDelay: 1000,
      messageDisplayTime: 2000,
      fadeInDuration: 600,
      fadeOutDuration: 600,
    },
    
    createdAt: new Date(),
    updatedAt: new Date(),
  });
}
