/**
 * Scenario Manager
 * 
 * Manages loading and switching between different poker hand scenarios.
 * Provides utilities for scenario selection, validation, and conversion.
 */

import { HandScenario } from '../types/HandScenario';
import { parseHandScenario, convertToGameFormat } from './handScenarioLoader';

// Import scenario JSON files
import sampleScenario from '../data/sampleHandScenario.json';
import scenario1 from '../data/scenario1.json';
import scenario2 from '../data/scenario2.json';
import scenario3 from '../data/scenario3.json';

/**
 * Available scenarios in the app
 */
export interface ScenarioInfo {
  id: string;
  title: string;
  description: string;
  difficulty: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT';
  tags: string[];
  concept?: string;
}

/**
 * All available scenarios
 */
const SCENARIOS_DATA = {
  'sample': sampleScenario,
  'scenario1': scenario1,
  'scenario2': scenario2,
  'scenario3': scenario3,
};

/**
 * Get list of all available scenarios
 */
export function getAvailableScenarios(): ScenarioInfo[] {
  return Object.entries(SCENARIOS_DATA).map(([key, data]) => ({
    id: data.id,
    title: data.title,
    description: data.description,
    difficulty: data.difficulty as 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT',
    tags: data.tags,
    concept: data.concept,
  }));
}

/**
 * Load a specific scenario by ID
 */
export function loadScenarioById(scenarioId: string): HandScenario | null {
  // Find the scenario data
  const scenarioData = Object.values(SCENARIOS_DATA).find(data => data.id === scenarioId);
  
  if (!scenarioData) {
    console.error(`Scenario not found: ${scenarioId}`);
    return null;
  }

  try {
    // Parse and validate the scenario
    return parseHandScenario(scenarioData);
  } catch (error) {
    console.warn(`Error loading scenario ${scenarioId}:`, error);
    // Return null for now, but don't crash the app
    return null;
  }
}

/**
 * Load a scenario by key (for backwards compatibility)
 */
export function loadScenarioByKey(key: keyof typeof SCENARIOS_DATA): HandScenario | null {
  const scenarioData = SCENARIOS_DATA[key];
  
  if (!scenarioData) {
    console.error(`Scenario key not found: ${key}`);
    return null;
  }

  try {
    return parseHandScenario(scenarioData);
  } catch (error) {
    console.error(`Error loading scenario ${key}:`, error);
    return null;
  }
}

/**
 * Convert scenario to game format for use in components
 */
export function getGameDataForScenario(scenarioId: string) {
  const scenario = loadScenarioById(scenarioId);
  
  if (!scenario) {
    return null;
  }

  // Convert to the format expected by existing components
  return {
    // Basic scenario info
    id: scenario.id,
    title: scenario.title,
    description: scenario.description,
    
    // Game state for PokerTable component
    holeCards: scenario.heroCards.map(card => `${card.displayRank}${card.displaySuit}`),
    position: scenario.heroPosition,
    potSize: scenario.potState.totalPotBB,
    actionHistory: scenario.actionHistory.preflop.map(action => 
      `${action.position} ${action.action.toLowerCase()}${action.amount ? ` ${action.amountBB}bb` : ''}`
    ).join(', '),
    
    // Animation data for VanishingChat
    chatMessages: scenario.animationSequence.actions
      .filter(action => action.showInChat)
      .map((action, index) => ({
        id: `${index + 1}`,
        position: action.position,
        action: action.action,
        amount: action.amountBB,
        delay: action.animationDelay || 0,
      })),
    
    // Player data for table display
    players: scenario.players.map(player => ({
      position: player.position,
      stack: player.stackSize,
      stackBB: player.stackSizeBB,
      isActive: player.isActive,
      isHero: player.isHero,
      isActing: player.isActing,
    })),
    
    // Game settings
    bigBlind: scenario.gameSettings.bigBlind,
    smallBlind: scenario.gameSettings.smallBlind,
    
    // Educational content
    tags: scenario.tags,
    difficulty: scenario.difficulty,
    concept: scenario.concept,
    analysis: scenario.analysis,
    
    // Decision point
    decisionPoint: scenario.decisionPoint,
    
    // Pot state
    potState: scenario.potState,
    
    // Metadata
    createdAt: scenario.createdAt,
  };
}

/**
 * Get scenarios by difficulty level
 */
export function getScenariosByDifficulty(difficulty: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT'): ScenarioInfo[] {
  return getAvailableScenarios().filter(scenario => scenario.difficulty === difficulty);
}

/**
 * Get scenarios by tag
 */
export function getScenariosByTag(tag: string): ScenarioInfo[] {
  return getAvailableScenarios().filter(scenario => scenario.tags.includes(tag));
}

/**
 * Get scenarios by concept
 */
export function getScenariosByConcept(concept: string): ScenarioInfo[] {
  return getAvailableScenarios().filter(scenario => 
    scenario.concept?.toLowerCase().includes(concept.toLowerCase())
  );
}

/**
 * Get a random scenario
 */
export function getRandomScenario(): ScenarioInfo | null {
  const scenarios = getAvailableScenarios();
  if (scenarios.length === 0) return null;
  
  const randomIndex = Math.floor(Math.random() * scenarios.length);
  return scenarios[randomIndex];
}

/**
 * Get the default scenario (sample scenario)
 */
export function getDefaultScenario() {
  return getGameDataForScenario('hand_001_utg_3bet_decision');
}

/**
 * Validate that all scenarios can be loaded
 */
export function validateAllScenarios(): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  Object.entries(SCENARIOS_DATA).forEach(([key, data]) => {
    try {
      parseHandScenario(data);
    } catch (error) {
      errors.push(`${key}: ${error}`);
    }
  });
  
  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * Get scenario statistics
 */
export function getScenarioStats() {
  const scenarios = getAvailableScenarios();
  
  const difficultyCount = scenarios.reduce((acc, scenario) => {
    acc[scenario.difficulty] = (acc[scenario.difficulty] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  const allTags = scenarios.flatMap(scenario => scenario.tags);
  const uniqueTags = [...new Set(allTags)];
  
  return {
    total: scenarios.length,
    byDifficulty: difficultyCount,
    uniqueTags: uniqueTags.length,
    allTags: uniqueTags,
  };
}
