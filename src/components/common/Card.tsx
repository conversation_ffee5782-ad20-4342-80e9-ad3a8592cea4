import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { CardProps } from '../../types';
import { Colors, Layout, BorderRadius, Typography } from '../../constants/theme';
import { useSettings } from '../../contexts/SettingsContext';

export const Card: React.FC<CardProps> = ({
  card,
  size = 'medium',
  style
}) => {
  const { cardColorScheme } = useSettings();

  /**
   * Get card size styles based on size prop
   * Reduced all sizes for better mobile layout
   */
  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          width: Layout.card.width * 0.6, // Reduced from 0.7
          height: Layout.card.height * 0.6,
        };
      case 'large':
        return {
          width: Layout.card.width * 1.1, // Reduced from 1.4
          height: Layout.card.height * 1.1,
        };
      default:
        return {
          width: Layout.card.width * 0.8, // Reduced from 1.0
          height: Layout.card.height * 0.8,
        };
    }
  };

  const getTextSize = () => {
    switch (size) {
      case 'small':
        return Typography.sizes.base;
      case 'large':
        return Typography.sizes['3xl'];
      default:
        return Typography.sizes.xl;
    }
  };

  const getSuitColor = (cardString: string) => {
    const suit = cardString.slice(-1);

    if (cardColorScheme === 'fourColor') {
      switch (suit) {
        case '♥': return Colors.cards.fourColor.hearts;   // Red
        case '♦': return Colors.cards.fourColor.diamonds; // Blue
        case '♣': return Colors.cards.fourColor.clubs;    // Black
        case '♠': return Colors.cards.fourColor.spades;   // Green
        default: return Colors.cards.traditional.black;
      }
    } else {
      // Traditional two-color scheme
      return (suit === '♥' || suit === '♦')
        ? Colors.cards.traditional.red
        : Colors.cards.traditional.black;
    }
  };

  const textColor = getSuitColor(card);

  // Split card into rank and suit for better layout
  const rank = card.slice(0, -1);
  const suit = card.slice(-1);

  // Get suit symbol for better display
  const getSuitSymbol = (suitChar: string) => {
    switch (suitChar) {
      case '♠': return '♠';
      case '♥': return '♥';
      case '♦': return '♦';
      case '♣': return '♣';
      default: return suitChar;
    }
  };

  const suitSymbol = getSuitSymbol(suit);

  return (
    <View style={[styles.cardContainer, getSizeStyles(), style]}>
      <View style={styles.card}>
        {/* Card background gradient effect */}
        <View style={styles.cardGradient} />

        <View style={styles.cardContent}>
          {/* Top left corner */}
          <View style={styles.topLeft}>
            <Text style={[styles.rankText, { color: textColor, fontSize: getTextSize() * 0.7 }]}>
              {rank}
            </Text>
            <Text style={[styles.suitText, { color: textColor, fontSize: getTextSize() * 0.8 }]}>
              {suitSymbol}
            </Text>
          </View>

          {/* Center area - keep clean */}
          <View style={styles.center}>
            {/* No center suit - cleaner look */}
          </View>

          {/* Bottom right corner (rotated) */}
          <View style={styles.bottomRight}>
            <Text style={[styles.rankText, styles.rotated, { color: textColor, fontSize: getTextSize() * 0.7 }]}>
              {rank}
            </Text>
            <Text style={[styles.suitText, styles.rotated, { color: textColor, fontSize: getTextSize() * 0.8 }]}>
              {suitSymbol}
            </Text>
          </View>
        </View>

        {/* Card border highlight */}
        <View style={styles.cardBorder} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    padding: 1,
  },
  card: {
    flex: 1,
    backgroundColor: '#ffffff',
    borderRadius: BorderRadius.md,
    position: 'relative',
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  cardGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: BorderRadius.md,
  },
  cardBorder: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
  },
  cardContent: {
    flex: 1,
    position: 'relative',
    zIndex: 2,
  },
  topLeft: {
    position: 'absolute',
    top: 4,
    left: 4,
    alignItems: 'flex-start',
  },
  center: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomRight: {
    position: 'absolute',
    bottom: 4,
    right: 4,
    alignItems: 'flex-end',
  },
  rankText: {
    fontWeight: Typography.weights.bold,
    textAlign: 'center',
    fontFamily: 'System',
    lineHeight: undefined,
  },
  suitText: {
    textAlign: 'center',
    fontWeight: Typography.weights.bold,
    lineHeight: undefined,
    marginTop: -2,
  },

  rotated: {
    transform: [{ rotate: '180deg' }],
  },
});
