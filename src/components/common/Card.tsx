import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { CardProps } from '../../types';
import { Colors, Layout, BorderRadius, Shadows, Typography } from '../../constants/theme';

export const Card: React.FC<CardProps> = ({ 
  card, 
  size = 'medium', 
  style 
}) => {
  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          width: Layout.card.width * 0.7,
          height: Layout.card.height * 0.7,
        };
      case 'large':
        return {
          width: Layout.card.width * 1.3,
          height: Layout.card.height * 1.3,
        };
      default:
        return {
          width: Layout.card.width,
          height: Layout.card.height,
        };
    }
  };

  const getTextSize = () => {
    switch (size) {
      case 'small':
        return Typography.sizes.base;
      case 'large':
        return Typography.sizes['2xl'];
      default:
        return Typography.sizes.lg;
    }
  };

  const isRedSuit = (cardString: string) => {
    return cardString.includes('♥') || cardString.includes('♦');
  };

  const textColor = isRedSuit(card) ? Colors.cards.red : Colors.cards.black;

  return (
    <View style={[styles.card, getSizeStyles(), style]}>
      <Text style={[styles.cardText, { color: textColor, fontSize: getTextSize() }]}>
        {card}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.cards.background,
    borderRadius: BorderRadius.md,
    justifyContent: 'center',
    alignItems: 'center',
    ...Shadows.md,
  },
  cardText: {
    fontWeight: Typography.weights.bold,
    textAlign: 'center',
  },
});
