import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { CardProps } from '../../types';
import { Colors, Layout, BorderRadius, Typography } from '../../constants/theme';

export const Card: React.FC<CardProps> = ({
  card,
  size = 'medium',
  style
}) => {
  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          width: Layout.card.width * 0.7,
          height: Layout.card.height * 0.7,
        };
      case 'large':
        return {
          width: Layout.card.width * 1.4,
          height: Layout.card.height * 1.4,
        };
      default:
        return {
          width: Layout.card.width,
          height: Layout.card.height,
        };
    }
  };

  const getTextSize = () => {
    switch (size) {
      case 'small':
        return Typography.sizes.base;
      case 'large':
        return Typography.sizes['3xl'];
      default:
        return Typography.sizes.xl;
    }
  };

  const isRedSuit = (cardString: string) => {
    return cardString.includes('♥') || cardString.includes('♦');
  };

  const textColor = isRedSuit(card) ? Colors.cards.red : Colors.cards.black;

  // Split card into rank and suit for better layout
  const rank = card.slice(0, -1);
  const suit = card.slice(-1);

  return (
    <View style={[styles.cardContainer, getSizeStyles(), style]}>
      <View style={styles.card}>
        <View style={styles.cardContent}>
          <View style={styles.topLeft}>
            <Text style={[styles.rankText, { color: textColor, fontSize: getTextSize() * 0.8 }]}>
              {rank}
            </Text>
            <Text style={[styles.suitText, { color: textColor, fontSize: getTextSize() * 0.6 }]}>
              {suit}
            </Text>
          </View>

          <View style={styles.center}>
            <Text style={[styles.centerSuit, { color: textColor, fontSize: getTextSize() * 1.2 }]}>
              {suit}
            </Text>
          </View>

          <View style={styles.bottomRight}>
            <Text style={[styles.rankText, styles.rotated, { color: textColor, fontSize: getTextSize() * 0.8 }]}>
              {rank}
            </Text>
            <Text style={[styles.suitText, styles.rotated, { color: textColor, fontSize: getTextSize() * 0.6 }]}>
              {suit}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    padding: 2,
  },
  card: {
    flex: 1,
    backgroundColor: Colors.cards.background,
    borderRadius: BorderRadius.lg,
    borderWidth: 1,
    borderColor: Colors.cards.border,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
  cardContent: {
    flex: 1,
    position: 'relative',
  },
  topLeft: {
    position: 'absolute',
    top: 6,
    left: 6,
    alignItems: 'center',
  },
  center: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomRight: {
    position: 'absolute',
    bottom: 6,
    right: 6,
    alignItems: 'center',
  },
  rankText: {
    fontWeight: Typography.weights.bold,
    textAlign: 'center',
    lineHeight: Typography.sizes.lg,
  },
  suitText: {
    textAlign: 'center',
    lineHeight: Typography.sizes.sm,
  },
  centerSuit: {
    fontWeight: Typography.weights.bold,
    textAlign: 'center',
  },
  rotated: {
    transform: [{ rotate: '180deg' }],
  },
});
