import React from 'react';
import { TouchableOpacity, Text, StyleSheet, View } from 'react-native';
import { ActionButtonProps } from '../../types';
import { Colors, Typography, BorderRadius, Layout } from '../../constants/theme';

export const ActionButton: React.FC<ActionButtonProps> = ({
  action,
  onPress,
  disabled = false,
  style,
}) => {
  const getButtonColors = () => {
    if (disabled) return { primary: Colors.actions.disabled, secondary: Colors.actions.disabled };

    switch (action) {
      case 'FOLD':
        return Colors.actions.fold;
      case 'CHECK':
      case 'CALL':
        return Colors.actions.call;
      case 'BET':
      case 'RAISE':
      case 'ALL_IN':
        return Colors.actions.raise;
      default:
        return { primary: Colors.actions.disabled, secondary: Colors.actions.disabled };
    }
  };

  const getButtonText = () => {
    switch (action) {
      case 'FOLD':
        return 'FOLD';
      case 'CHECK':
        return 'CHECK';
      case 'CALL':
        return 'CALL';
      case 'BET':
        return 'BET';
      case 'RAISE':
        return 'RAISE';
      case 'ALL_IN':
        return 'ALL IN';
      default:
        return action;
    }
  };

  const getButtonIcon = () => {
    switch (action) {
      case 'FOLD':
        return '✕';
      case 'CHECK':
      case 'CALL':
        return '✓';
      case 'BET':
      case 'RAISE':
        return '↗';
      case 'ALL_IN':
        return '⚡';
      default:
        return '';
    }
  };

  const handlePress = () => {
    if (!disabled) {
      onPress(action);
    }
  };

  const colors = getButtonColors();

  return (
    <TouchableOpacity
      style={[
        styles.button,
        { backgroundColor: colors.primary },
        disabled && styles.disabled,
        style,
      ]}
      onPress={handlePress}
      disabled={disabled}
      activeOpacity={0.85}
    >
      <View style={styles.buttonContent}>
        <Text style={styles.buttonIcon}>{getButtonIcon()}</Text>
        <Text style={styles.buttonText}>{getButtonText()}</Text>
      </View>

      {/* Subtle highlight overlay */}
      <View style={[styles.highlight, { backgroundColor: colors.secondary }]} />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    flex: 1,
    height: Layout.button.height,
    borderRadius: BorderRadius.lg,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 6,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 6,
    zIndex: 2,
  },
  buttonIcon: {
    fontSize: Typography.sizes.base,
    color: Colors.text.primary,
    fontWeight: Typography.weights.bold,
  },
  buttonText: {
    fontSize: Typography.sizes.sm,
    fontWeight: Typography.weights.bold,
    color: Colors.text.primary,
    letterSpacing: 0.5,
  },
  highlight: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '30%',
    opacity: 0.2,
    zIndex: 1,
  },
  disabled: {
    opacity: 0.5,
  },
});
