import React from 'react';
import { TouchableOpacity, Text, StyleSheet } from 'react-native';
import { ActionButtonProps } from '../../types';
import { Colors, Typography, BorderRadius, Shadows, Layout } from '../../constants/theme';

export const ActionButton: React.FC<ActionButtonProps> = ({
  action,
  onPress,
  disabled = false,
  style,
}) => {
  const getButtonColor = () => {
    if (disabled) return Colors.actions.disabled;
    
    switch (action) {
      case 'FOLD':
        return Colors.actions.fold;
      case 'CHECK':
      case 'CALL':
        return Colors.actions.call;
      case 'BET':
      case 'RAISE':
      case 'ALL_IN':
        return Colors.actions.raise;
      default:
        return Colors.actions.disabled;
    }
  };

  const getButtonText = () => {
    switch (action) {
      case 'FOLD':
        return 'FOLD';
      case 'CHECK':
        return 'CHECK';
      case 'CALL':
        return 'CALL';
      case 'BET':
        return 'BET';
      case 'RAISE':
        return 'RAISE';
      case 'ALL_IN':
        return 'ALL IN';
      default:
        return action;
    }
  };

  const handlePress = () => {
    if (!disabled) {
      onPress(action);
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.button,
        { backgroundColor: getButtonColor() },
        disabled && styles.disabled,
        style,
      ]}
      onPress={handlePress}
      disabled={disabled}
      activeOpacity={0.8}
    >
      <Text style={styles.buttonText}>{getButtonText()}</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    flex: 1,
    height: Layout.button.height,
    borderRadius: BorderRadius.lg,
    justifyContent: 'center',
    alignItems: 'center',
    ...Shadows.md,
  },
  buttonText: {
    fontSize: Typography.sizes.base,
    fontWeight: Typography.weights.bold,
    color: Colors.text.primary,
  },
  disabled: {
    opacity: 0.6,
  },
});
