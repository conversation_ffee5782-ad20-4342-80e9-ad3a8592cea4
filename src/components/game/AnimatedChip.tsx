import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import { Colors, Typography, Spacing, BorderRadius } from '../../constants/theme';

interface AnimatedChipProps {
  amount: number;
  fromPosition: { x: number; y: number };
  toPosition: { x: number; y: number };
  delay?: number;
  onComplete?: () => void;
}

export const AnimatedChip: React.FC<AnimatedChipProps> = ({
  amount,
  fromPosition,
  toPosition,
  delay = 0,
  onComplete,
}) => {
  const translateX = useRef(new Animated.Value(fromPosition.x)).current;
  const translateY = useRef(new Animated.Value(fromPosition.y)).current;
  const opacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.sequence([
      Animated.delay(delay),
      Animated.timing(opacity, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(Animated.createAnimatedComponent(View), {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.parallel([
        Animated.timing(translateX, {
          toValue: toPosition.x,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(translateY, {
          toValue: toPosition.y,
          duration: 800,
          useNativeDriver: true,
        }),
      ]),
    ]);

    animation.start(() => {
      if (onComplete) {
        onComplete();
      }
    });
  }, []);

  const formatAmount = (amt: number) => {
    if (amt >= 1000) {
      return `${(amt / 1000).toFixed(1)}K`;
    }
    return amt.toString();
  };

  return (
    <Animated.View
      style={[
        styles.chip,
        {
          transform: [{ translateX }, { translateY }],
          opacity,
        },
      ]}
    >
      <Text style={styles.chipText}>{formatAmount(amount)}</Text>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  chip: {
    position: 'absolute',
    backgroundColor: Colors.table.chip,
    borderRadius: BorderRadius.full,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
    borderWidth: 2,
    borderColor: Colors.table.chipShadow,
  },
  chipText: {
    fontSize: Typography.sizes.xs,
    fontWeight: Typography.weights.bold,
    color: Colors.primary.dark,
  },
});
