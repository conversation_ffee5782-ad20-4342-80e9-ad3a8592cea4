import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Colors, Typography, Spacing, BorderRadius } from '../../constants/theme';

interface PlayerProps {
  name: string;
  chips: number;
  position: string;
  isActive?: boolean;
  isDealer?: boolean;
  isFolded?: boolean;
  isHero?: boolean;
  hasCards?: boolean;
}

export const Player: React.FC<PlayerProps> = ({
  name,
  chips,
  position,
  isActive = false,
  isDealer = false,
  isFolded = false,
  isHero = false,
  hasCards = true,
}) => {
  const getPlayerColor = () => {
    if (isFolded) return Colors.players.folded;
    if (isHero) return Colors.players.hero;
    if (isActive) return Colors.players.active;
    if (isDealer) return Colors.players.dealer;
    return Colors.players.opponent;
  };

  const formatChips = (amount: number) => {
    if (amount >= 1000) {
      return `${(amount / 1000).toFixed(1)}K`;
    }
    return amount.toString();
  };

  return (
    <View style={[styles.container, { borderColor: getPlayerColor() }]}>
      {/* Player info */}
      <View style={styles.playerInfo}>
        <Text style={[styles.playerName, { color: getPlayerColor() }]} numberOfLines={1}>
          {name}
        </Text>
        <Text style={styles.chips}>{formatChips(chips)}</Text>
        {isDealer && <Text style={styles.dealerButton}>D</Text>}
      </View>
      
      {/* Cards placeholder */}
      {hasCards && !isFolded && (
        <View style={styles.cardsContainer}>
          <View style={[styles.cardBack, isHero && styles.heroCard]} />
          <View style={[styles.cardBack, styles.cardBack2, isHero && styles.heroCard]} />
        </View>
      )}
      
      {/* Position indicator */}
      <Text style={styles.position}>{position}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    padding: Spacing.sm,
    borderRadius: BorderRadius.lg,
    borderWidth: 2,
    backgroundColor: Colors.table.seat,
    minWidth: 80,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  playerInfo: {
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  playerName: {
    fontSize: Typography.sizes.xs,
    fontWeight: Typography.weights.bold,
    marginBottom: 2,
  },
  chips: {
    fontSize: Typography.sizes.xs,
    color: Colors.text.secondary,
    fontWeight: Typography.weights.medium,
  },
  dealerButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: Colors.players.dealer,
    color: Colors.primary.dark,
    fontSize: Typography.sizes.xs,
    fontWeight: Typography.weights.bold,
    width: 16,
    height: 16,
    borderRadius: 8,
    textAlign: 'center',
    lineHeight: 16,
  },
  cardsContainer: {
    flexDirection: 'row',
    marginBottom: Spacing.xs,
  },
  cardBack: {
    width: 20,
    height: 28,
    backgroundColor: Colors.status.info,
    borderRadius: 3,
    borderWidth: 1,
    borderColor: Colors.cards.border,
  },
  cardBack2: {
    marginLeft: -8,
  },
  heroCard: {
    backgroundColor: Colors.players.hero,
  },
  position: {
    fontSize: Typography.sizes.xs,
    color: Colors.text.muted,
    fontWeight: Typography.weights.medium,
  },
});
