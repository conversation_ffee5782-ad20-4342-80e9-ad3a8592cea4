import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Colors, Typography, Spacing, BorderRadius } from '../../constants/theme';

interface PlayerProps {
  chips: number;
  position: string;
  isActive?: boolean;
  isDealer?: boolean;
  isFolded?: boolean;
  isHero?: boolean;
  hasCards?: boolean;
}

export const Player: React.FC<PlayerProps> = ({
  chips,
  position,
  isActive = false,
  isDealer = false,
  isFolded = false,
  isHero = false,
  hasCards = true,
}) => {
  const getPlayerColor = () => {
    if (isFolded) return Colors.players.folded;
    if (isHero) return Colors.players.hero;
    if (isActive) return Colors.players.active;
    if (isDealer) return Colors.players.dealer;
    return Colors.players.opponent;
  };

  const formatChips = (amount: number) => {
    if (amount >= 1000) {
      return `${(amount / 1000).toFixed(1)}K`;
    }
    return amount.toString();
  };

  return (
    <View style={[styles.container, { borderColor: getPlayerColor() }]}>
      {/* Position and chips */}
      <View style={styles.playerInfo}>
        <Text style={[styles.position, { color: getPlayerColor() }]}>
          {position}
        </Text>
        <Text style={styles.chips}>{formatChips(chips)}</Text>
        {isDealer && <Text style={styles.dealerButton}>D</Text>}
      </View>

      {/* Cards placeholder */}
      {hasCards && !isFolded && (
        <View style={styles.cardsContainer}>
          <View style={[styles.cardBack, isHero && styles.heroCard]} />
          <View style={[styles.cardBack, styles.cardBack2, isHero && styles.heroCard]} />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    padding: Spacing.xs,
    borderRadius: BorderRadius.md,
    borderWidth: 1.5,
    backgroundColor: Colors.table.seat,
    minWidth: 60,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  playerInfo: {
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  position: {
    fontSize: Typography.sizes.sm,
    fontWeight: Typography.weights.bold,
    marginBottom: 2,
  },
  chips: {
    fontSize: Typography.sizes.xs,
    color: Colors.text.secondary,
    fontWeight: Typography.weights.medium,
  },
  dealerButton: {
    position: 'absolute',
    top: -6,
    right: -6,
    backgroundColor: Colors.players.dealer,
    color: Colors.primary.dark,
    fontSize: Typography.sizes.xs,
    fontWeight: Typography.weights.bold,
    width: 14,
    height: 14,
    borderRadius: 7,
    textAlign: 'center',
    lineHeight: 14,
  },
  cardsContainer: {
    flexDirection: 'row',
  },
  cardBack: {
    width: 16,
    height: 22,
    backgroundColor: Colors.status.info,
    borderRadius: 2,
    borderWidth: 1,
    borderColor: Colors.cards.border,
  },
  cardBack2: {
    marginLeft: -6,
  },
  heroCard: {
    backgroundColor: Colors.players.hero,
  },
});
