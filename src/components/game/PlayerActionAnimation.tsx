import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import { Colors, Typography, Spacing } from '../../constants/theme';

interface PlayerActionAnimationProps {
  playerId: string;
  position: string;
  action: 'FOLD' | 'CALL' | 'RAISE' | 'CHECK' | 'BET' | 'ALL_IN';
  amount?: number;
  isActive: boolean;
  duration?: number;
  onComplete?: () => void;
}

/**
 * PlayerActionAnimation Component
 * 
 * Displays different animations for each poker action type:
 * - FOLD: Red pulse with fade out
 * - CALL: Blue steady glow
 * - RAISE: Green pulsing with scale effect
 * - CHECK: Yellow brief flash
 * - BET: Green steady glow
 * - ALL_IN: Rainbow pulsing effect
 */
export const PlayerActionAnimation: React.FC<PlayerActionAnimationProps> = ({
  playerId,
  position,
  action,
  amount,
  isActive,
  duration = 2000,
  onComplete,
}) => {
  // Animation values
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const colorAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (!isActive) {
      // Reset animations when not active
      pulseAnim.setValue(1);
      scaleAnim.setValue(1);
      opacityAnim.setValue(0);
      colorAnim.setValue(0);
      return;
    }

    // Start the appropriate animation based on action type
    startActionAnimation();
  }, [isActive, action]);

  /**
   * Start the animation sequence based on action type
   */
  const startActionAnimation = () => {
    switch (action) {
      case 'FOLD':
        animateFold();
        break;
      case 'CALL':
        animateCall();
        break;
      case 'RAISE':
      case 'BET':
        animateRaise();
        break;
      case 'CHECK':
        animateCheck();
        break;
      case 'ALL_IN':
        animateAllIn();
        break;
      default:
        animateDefault();
    }
  };

  /**
   * FOLD Animation: Red pulse that fades out
   */
  const animateFold = () => {
    Animated.sequence([
      // Fade in with red color
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      // Pulse effect
      Animated.timing(pulseAnim, {
        toValue: 1.2,
        duration: 400,
        useNativeDriver: true,
      }),
      // Fade out
      Animated.parallel([
        Animated.timing(pulseAnim, {
          toValue: 0.8,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0.3,
          duration: 800,
          useNativeDriver: true,
        }),
      ]),
    ]).start(() => {
      onComplete?.();
    });
  };

  /**
   * CALL Animation: Blue steady glow
   */
  const animateCall = () => {
    Animated.sequence([
      // Fade in with blue glow
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      }),
      // Steady glow
      Animated.timing(pulseAnim, {
        toValue: 1.1,
        duration: 600,
        useNativeDriver: true,
      }),
      // Hold for a moment
      Animated.delay(600),
      // Fade out
      Animated.timing(opacityAnim, {
        toValue: 0,
        duration: 400,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onComplete?.();
    });
  };

  /**
   * RAISE/BET Animation: Green pulsing with scale effect
   */
  const animateRaise = () => {
    Animated.sequence([
      // Fade in
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      // Double pulse with scale
      Animated.loop(
        Animated.sequence([
          Animated.parallel([
            Animated.timing(pulseAnim, {
              toValue: 1.3,
              duration: 400,
              useNativeDriver: true,
            }),
            Animated.timing(scaleAnim, {
              toValue: 1.1,
              duration: 400,
              useNativeDriver: true,
            }),
          ]),
          Animated.parallel([
            Animated.timing(pulseAnim, {
              toValue: 1,
              duration: 400,
              useNativeDriver: true,
            }),
            Animated.timing(scaleAnim, {
              toValue: 1,
              duration: 400,
              useNativeDriver: true,
            }),
          ]),
        ]),
        { iterations: 2 }
      ),
      // Fade out
      Animated.timing(opacityAnim, {
        toValue: 0,
        duration: 400,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onComplete?.();
    });
  };

  /**
   * CHECK Animation: Yellow brief flash
   */
  const animateCheck = () => {
    Animated.sequence([
      // Quick flash
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(pulseAnim, {
        toValue: 1.15,
        duration: 300,
        useNativeDriver: true,
      }),
      // Quick fade
      Animated.parallel([
        Animated.timing(opacityAnim, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
      ]),
    ]).start(() => {
      onComplete?.();
    });
  };

  /**
   * ALL_IN Animation: Rainbow pulsing effect
   */
  const animateAllIn = () => {
    Animated.sequence([
      // Fade in
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      // Rainbow color cycling with intense pulsing
      Animated.loop(
        Animated.sequence([
          Animated.parallel([
            Animated.timing(colorAnim, {
              toValue: 1,
              duration: 300,
              useNativeDriver: false,
            }),
            Animated.timing(pulseAnim, {
              toValue: 1.4,
              duration: 300,
              useNativeDriver: true,
            }),
            Animated.timing(scaleAnim, {
              toValue: 1.2,
              duration: 300,
              useNativeDriver: true,
            }),
          ]),
          Animated.parallel([
            Animated.timing(colorAnim, {
              toValue: 0,
              duration: 300,
              useNativeDriver: false,
            }),
            Animated.timing(pulseAnim, {
              toValue: 1,
              duration: 300,
              useNativeDriver: true,
            }),
            Animated.timing(scaleAnim, {
              toValue: 1,
              duration: 300,
              useNativeDriver: true,
            }),
          ]),
        ]),
        { iterations: 3 }
      ),
      // Fade out
      Animated.timing(opacityAnim, {
        toValue: 0,
        duration: 400,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onComplete?.();
    });
  };

  /**
   * Default Animation: Simple highlight
   */
  const animateDefault = () => {
    Animated.sequence([
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      }),
      Animated.delay(1200),
      Animated.timing(opacityAnim, {
        toValue: 0,
        duration: 400,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onComplete?.();
    });
  };

  /**
   * Get the color for the action type
   */
  const getActionColor = () => {
    switch (action) {
      case 'FOLD':
        return Colors.actions.fold.primary;
      case 'CALL':
        return Colors.actions.call.primary;
      case 'RAISE':
      case 'BET':
        return Colors.actions.raise.primary;
      case 'CHECK':
        return '#FFD700'; // Gold
      case 'ALL_IN':
        // Rainbow effect for all-in
        return colorAnim.interpolate({
          inputRange: [0, 0.33, 0.66, 1],
          outputRange: ['#FF0000', '#00FF00', '#0000FF', '#FF0000'],
        });
      default:
        return Colors.primary.light;
    }
  };

  if (!isActive) {
    return null;
  }

  return (
    <Animated.View
      style={[
        styles.animationOverlay,
        {
          opacity: opacityAnim,
          transform: [
            { scale: scaleAnim },
            { scale: pulseAnim },
          ],
        },
      ]}
    >
      <Animated.View
        style={[
          styles.actionIndicator,
          {
            backgroundColor: getActionColor(),
            shadowColor: getActionColor(),
          },
        ]}
      >
        <Text style={styles.actionText}>{action}</Text>
        {amount && (
          <Text style={styles.amountText}>{amount}bb</Text>
        )}
      </Animated.View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  // Overlay that covers the seat card area
  animationOverlay: {
    position: 'absolute',
    top: -10,
    left: -10,
    right: -10,
    bottom: -10,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 100,
  },
  // Action indicator bubble
  actionIndicator: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: 12,
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.8,
    shadowRadius: 8,
    elevation: 8,
    minWidth: 50,
    alignItems: 'center',
  },
  // Action text (FOLD, CALL, etc.)
  actionText: {
    fontSize: Typography.sizes.xs,
    fontWeight: Typography.weights.bold,
    color: Colors.text.primary,
    textAlign: 'center',
  },
  // Amount text for bets/raises
  amountText: {
    fontSize: Typography.sizes.xs,
    fontWeight: Typography.weights.semibold,
    color: Colors.text.primary,
    textAlign: 'center',
    marginTop: 1,
  },
});
