import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { Player } from './Player';
import { Card } from '../common/Card';
import { SeatCard } from './SeatCard';
import { Colors, Typography, Spacing, BorderRadius } from '../../constants/theme';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Table specifications
const TABLE_SPECS = {
  innerWidthRatio: 0.85,
  innerHeightRatio: 0.60,
  railStroke: 6,
  seatWidth: 44,
  seatHeight: 56,
  seatRadius: 8,
  minClearance: 4,
};

interface PokerTableProps {
  heroCards: string[];
  heroPosition: string;
  potSize: number;
  actionHistory: string;
}

interface SeatPosition {
  x: number;
  y: number;
  position: string;
  stack: number;
  isActive?: boolean;
  isHero?: boolean;
}

// Calculate seat positions using a simpler, more reliable approach
const calculateSeatPositions = (
  tableWidth: number,
  tableHeight: number,
  seatCount: number = 6
): SeatPosition[] => {
  // Use a larger, more conservative ellipse to prevent overlapping
  const centerX = tableWidth / 2;
  const centerY = tableHeight / 2;

  // Make the ellipse much larger to prevent overlapping
  const radiusX = (tableWidth * 0.35); // Increased from calculated value
  const radiusY = (tableHeight * 0.25); // Increased from calculated value

  const positions: SeatPosition[] = [];

  // 6-max positions and stacks
  const seatData = [
    { position: 'UTG', stack: 2500 },
    { position: 'MP', stack: 1800 },
    { position: 'CO', stack: 3200 },
    { position: 'Button', stack: 2100 },
    { position: 'SB', stack: 950 },
    { position: 'BB', stack: 1650 },
  ];

  // Manually position seats to avoid overlapping
  const seatPositions = [
    { x: centerX, y: centerY - radiusY }, // UTG - top
    { x: centerX + radiusX * 0.8, y: centerY - radiusY * 0.5 }, // MP - top right
    { x: centerX + radiusX * 0.8, y: centerY + radiusY * 0.5 }, // CO - bottom right
    { x: centerX, y: centerY + radiusY }, // Button - bottom
    { x: centerX - radiusX * 0.8, y: centerY + radiusY * 0.5 }, // SB - bottom left
    { x: centerX - radiusX * 0.8, y: centerY - radiusY * 0.5 }, // BB - top left
  ];

  for (let i = 0; i < Math.min(seatCount, seatPositions.length); i++) {
    positions.push({
      x: seatPositions[i].x,
      y: seatPositions[i].y,
      position: seatData[i]?.position || `Seat ${i + 1}`,
      stack: seatData[i]?.stack || 1000,
      isHero: seatData[i]?.position === 'Button',
      isActive: seatData[i]?.position === 'UTG', // UTG is currently acting
    });
  }

  return positions;
};

export const PokerTable: React.FC<PokerTableProps> = ({
  heroCards,
  heroPosition,
  potSize,
  actionHistory,
}) => {
  const formatPot = (pot: number) => {
    if (pot >= 1000) {
      return (pot / 1000).toFixed(1);
    }
    return pot.toString();
  };

  // Calculate table dimensions - make it larger to prevent seat overlap
  const tableWidth = screenWidth * 0.95;
  const tableHeight = screenHeight * 0.4; // Fixed height instead of ratio-based

  // Get seat positions using elliptical algorithm
  const seatPositions = calculateSeatPositions(tableWidth, tableHeight, 6);

  return (
    <View style={styles.container}>
      {/* Poker Table with elliptical design */}
      <View style={[styles.table, { width: tableWidth, height: tableHeight }]}>
        {/* Table rail (outer border) */}
        <View style={[styles.tableRail, {
          width: tableWidth,
          height: tableHeight,
          borderRadius: tableWidth * 0.5,
        }]} />

        {/* Table felt (inner area) */}
        <View style={[styles.tableFelt, {
          width: tableWidth * TABLE_SPECS.innerWidthRatio,
          height: tableHeight * TABLE_SPECS.innerHeightRatio,
          borderRadius: (tableWidth * TABLE_SPECS.innerWidthRatio) * 0.5,
        }]} />

        {/* Seat cards positioned using elliptical algorithm */}
        {seatPositions.map((seat, index) => (
          <SeatCard
            key={seat.position}
            position={seat.position}
            stack={seat.stack}
            isActive={seat.isActive}
            isHero={seat.isHero}
            style={{
              left: seat.x - 20, // Center the 40px touch target
              top: seat.y - 26,   // Center the 52px touch target
            }}
          />
        ))}

        {/* Center area with community cards and pot - positioned to avoid seat overlap */}
        <View style={[styles.centerArea, {
          left: tableWidth * 0.25,
          top: tableHeight * 0.35,
          width: tableWidth * 0.5,
          height: tableHeight * 0.3,
        }]}>
          {/* Community cards area */}
          <View style={styles.communityCards}>
            <Text style={styles.communityLabel}>Community Cards</Text>
            <View style={styles.cardSlots}>
              {[1, 2, 3, 4, 5].map((i) => (
                <View key={i} style={styles.cardSlot} />
              ))}
            </View>
          </View>

          {/* Pot container */}
          <View style={styles.potContainer}>
            <Text style={styles.potLabel}>POT</Text>
            <Text style={styles.potAmount}>{formatPot(potSize)}bb</Text>
          </View>
        </View>
      </View>

      {/* Hero's cards */}
      <View style={styles.heroCardsContainer}>
        <Text style={styles.heroCardsLabel}>Your Cards</Text>
        <View style={styles.heroCards}>
          {heroCards.map((card, index) => (
            <Card key={index} card={card} size="large" style={styles.heroCard} />
          ))}
        </View>
      </View>

      {/* Action history */}
      <View style={styles.actionContainer}>
        <Text style={styles.actionText}>{actionHistory}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.primary.dark,
    justifyContent: 'center',
    alignItems: 'center',
  },
  table: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  tableFelt: {
    position: 'absolute',
    backgroundColor: Colors.table.felt,
    // Inner shadow effect
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 4,
  },
  tableRail: {
    position: 'absolute',
    borderWidth: TABLE_SPECS.railStroke,
    borderColor: '#B5651D', // Rail color as specified
    backgroundColor: 'transparent',
  },
  centerArea: {
    position: 'absolute',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  potContainer: {
    alignItems: 'center',
    backgroundColor: Colors.table.feltDark,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.md,
  },
  potLabel: {
    fontSize: Typography.sizes.sm,
    color: Colors.text.secondary,
    fontWeight: Typography.weights.medium,
  },
  potAmount: {
    fontSize: Typography.sizes.lg,
    color: Colors.table.chip,
    fontWeight: Typography.weights.bold,
  },
  communityCards: {
    alignItems: 'center',
  },
  communityLabel: {
    fontSize: Typography.sizes.xs,
    color: Colors.text.muted,
    marginBottom: Spacing.xs,
  },
  cardSlots: {
    flexDirection: 'row',
    gap: Spacing.xs,
  },
  cardSlot: {
    width: 30,
    height: 42,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: Colors.table.feltDark,
    borderStyle: 'dashed',
  },

  heroCardsContainer: {
    alignItems: 'center',
    marginTop: Spacing.lg,
    backgroundColor: Colors.primary.medium,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.lg,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 6,
  },
  heroCardsLabel: {
    fontSize: Typography.sizes.base,
    color: Colors.text.accent,
    fontWeight: Typography.weights.semibold,
    marginBottom: Spacing.sm,
  },
  heroCards: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  heroCard: {
    marginHorizontal: 0,
  },
  actionContainer: {
    marginTop: Spacing.md,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.sm,
    backgroundColor: Colors.primary.medium,
    borderRadius: BorderRadius.md,
    maxWidth: screenWidth * 0.9,
  },
  actionText: {
    fontSize: Typography.sizes.sm,
    color: Colors.text.primary,
    textAlign: 'center',
    fontWeight: Typography.weights.medium,
  },
});
