import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { Player } from './Player';
import { Card } from '../common/Card';
import { SeatCard } from './SeatCard';
import { Colors, Typography, Spacing, BorderRadius } from '../../constants/theme';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Table specifications
const TABLE_SPECS = {
  innerWidthRatio: 0.85,
  innerHeightRatio: 0.60,
  railStroke: 6,
  seatWidth: 44,
  seatHeight: 56,
  seatRadius: 8,
  minClearance: 4,
};

interface PokerTableProps {
  heroCards: string[];
  heroPosition: string;
  potSize: number;
  actionHistory: string;
}

interface SeatPosition {
  x: number;
  y: number;
  position: string;
  stack: number;
  isActive?: boolean;
  isHero?: boolean;
}

// Calculate seat positions using elliptical algorithm
const calculateSeatPositions = (
  tableWidth: number,
  tableHeight: number,
  seatCount: number = 6
): SeatPosition[] => {
  const innerWidth = tableWidth * TABLE_SPECS.innerWidthRatio;
  const innerHeight = tableHeight * TABLE_SPECS.innerHeightRatio;

  // Calculate inner ellipse semi-axes with clearance
  const a = innerWidth / 2 - TABLE_SPECS.seatWidth / 2 - TABLE_SPECS.railStroke - TABLE_SPECS.minClearance;
  const b = innerHeight / 2 - TABLE_SPECS.seatHeight / 2 - TABLE_SPECS.railStroke - TABLE_SPECS.minClearance;

  const cx = tableWidth / 2;
  const cy = tableHeight / 2;

  const positions: SeatPosition[] = [];
  const angleOffset = -Math.PI / 2; // Start from top (-90°)

  // 6-max positions and stacks
  const seatData = [
    { position: 'UTG', stack: 2500 },
    { position: 'MP', stack: 1800 },
    { position: 'CO', stack: 3200 },
    { position: 'Button', stack: 2100 },
    { position: 'SB', stack: 950 },
    { position: 'BB', stack: 1650 },
  ];

  for (let i = 0; i < seatCount; i++) {
    const theta = angleOffset + (2 * Math.PI * i) / seatCount;
    const x = cx + a * Math.cos(theta);
    const y = cy + b * Math.sin(theta);

    positions.push({
      x,
      y,
      position: seatData[i]?.position || `Seat ${i + 1}`,
      stack: seatData[i]?.stack || 1000,
      isHero: seatData[i]?.position === 'Button',
      isActive: seatData[i]?.position === 'UTG', // UTG is currently acting
    });
  }

  return positions;
};

export const PokerTable: React.FC<PokerTableProps> = ({
  heroCards,
  heroPosition,
  potSize,
  actionHistory,
}) => {
  const formatPot = (pot: number) => {
    if (pot >= 1000) {
      return (pot / 1000).toFixed(1);
    }
    return pot.toString();
  };

  // Calculate table dimensions based on specifications
  const tableWidth = screenWidth * 0.9;
  const tableHeight = tableWidth * TABLE_SPECS.innerHeightRatio / TABLE_SPECS.innerWidthRatio;

  // Get seat positions using elliptical algorithm
  const seatPositions = calculateSeatPositions(tableWidth, tableHeight, 6);

  return (
    <View style={styles.container}>
      {/* Poker Table with elliptical design */}
      <View style={[styles.table, { width: tableWidth, height: tableHeight }]}>
        {/* Table rail (outer border) */}
        <View style={[styles.tableRail, {
          width: tableWidth,
          height: tableHeight,
          borderRadius: tableWidth * 0.5,
        }]} />

        {/* Table felt (inner area) */}
        <View style={[styles.tableFelt, {
          width: tableWidth * TABLE_SPECS.innerWidthRatio,
          height: tableHeight * TABLE_SPECS.innerHeightRatio,
          borderRadius: (tableWidth * TABLE_SPECS.innerWidthRatio) * 0.5,
        }]} />

        {/* Seat cards positioned using elliptical algorithm */}
        {seatPositions.map((seat, index) => (
          <SeatCard
            key={seat.position}
            position={seat.position}
            stack={seat.stack}
            isActive={seat.isActive}
            isHero={seat.isHero}
            style={{
              left: seat.x - 24, // Center the 48px touch target
              top: seat.y - 32,   // Center the 64px touch target
            }}
          />
        ))}

        {/* Center area with community cards and pot */}
        <View style={[styles.centerArea, {
          left: tableWidth * 0.2,
          top: tableHeight * 0.25,
          width: tableWidth * 0.6,
          height: tableHeight * 0.5,
        }]}>
          {/* Community cards area (moved up to avoid overlap) */}
          <View style={styles.communityCards}>
            <Text style={styles.communityLabel}>Community Cards</Text>
            <View style={styles.cardSlots}>
              {[1, 2, 3, 4, 5].map((i) => (
                <View key={i} style={styles.cardSlot} />
              ))}
            </View>
          </View>

          {/* Pot container (moved up 16px as specified) */}
          <View style={[styles.potContainer, { marginTop: 16 }]}>
            <Text style={styles.potLabel}>POT</Text>
            <Text style={styles.potAmount}>{formatPot(potSize)}bb</Text>
          </View>
        </View>
      </View>

      {/* Hero's cards */}
      <View style={styles.heroCardsContainer}>
        <Text style={styles.heroCardsLabel}>Your Cards</Text>
        <View style={styles.heroCards}>
          {heroCards.map((card, index) => (
            <Card key={index} card={card} size="large" style={styles.heroCard} />
          ))}
        </View>
      </View>

      {/* Action history */}
      <View style={styles.actionContainer}>
        <Text style={styles.actionText}>{actionHistory}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.primary.dark,
    justifyContent: 'center',
    alignItems: 'center',
  },
  table: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  tableFelt: {
    position: 'absolute',
    backgroundColor: Colors.table.felt,
    // Inner shadow effect
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 4,
  },
  tableRail: {
    position: 'absolute',
    borderWidth: TABLE_SPECS.railStroke,
    borderColor: '#B5651D', // Rail color as specified
    backgroundColor: 'transparent',
  },
  centerArea: {
    position: 'absolute',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  potContainer: {
    alignItems: 'center',
    backgroundColor: Colors.table.feltDark,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.md,
  },
  potLabel: {
    fontSize: Typography.sizes.sm,
    color: Colors.text.secondary,
    fontWeight: Typography.weights.medium,
  },
  potAmount: {
    fontSize: Typography.sizes.lg,
    color: Colors.table.chip,
    fontWeight: Typography.weights.bold,
  },
  communityCards: {
    alignItems: 'center',
  },
  communityLabel: {
    fontSize: Typography.sizes.xs,
    color: Colors.text.muted,
    marginBottom: Spacing.xs,
  },
  cardSlots: {
    flexDirection: 'row',
    gap: Spacing.xs,
  },
  cardSlot: {
    width: 30,
    height: 42,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: Colors.table.feltDark,
    borderStyle: 'dashed',
  },

  heroCardsContainer: {
    alignItems: 'center',
    marginTop: Spacing.lg,
    backgroundColor: Colors.primary.medium,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.lg,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 6,
  },
  heroCardsLabel: {
    fontSize: Typography.sizes.base,
    color: Colors.text.accent,
    fontWeight: Typography.weights.semibold,
    marginBottom: Spacing.sm,
  },
  heroCards: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  heroCard: {
    marginHorizontal: 0,
  },
  actionContainer: {
    marginTop: Spacing.md,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.sm,
    backgroundColor: Colors.primary.medium,
    borderRadius: BorderRadius.md,
    maxWidth: screenWidth * 0.9,
  },
  actionText: {
    fontSize: Typography.sizes.sm,
    color: Colors.text.primary,
    textAlign: 'center',
    fontWeight: Typography.weights.medium,
  },
});
