import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { Player } from './Player';
import { Card } from '../common/Card';
import { Colors, Typography, Spacing, BorderRadius } from '../../constants/theme';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface PokerTableProps {
  heroCards: string[];
  heroPosition: string;
  potSize: number;
  actionHistory: string;
}

export const PokerTable: React.FC<PokerTableProps> = ({
  heroCards,
  heroPosition,
  potSize,
  actionHistory,
}) => {
  // Sample players for the table
  const players = [
    { chips: 2500, position: 'UTG', isActive: false, isDealer: false, isFolded: false },
    { chips: 1800, position: 'MP', isActive: true, isDealer: false, isFolded: false },
    { chips: 3200, position: 'CO', isActive: false, isDealer: true, isFolded: false },
    { chips: 950, position: 'SB', isActive: false, isDealer: false, isFolded: true },
    { chips: 2100, position: heroPosition, isActive: false, isDealer: false, isFolded: false, isHero: true },
  ];

  const formatPot = (amount: number) => {
    if (amount >= 1000) {
      return `${(amount / 1000).toFixed(1)}K`;
    }
    return amount.toString();
  };

  return (
    <View style={styles.container}>
      {/* Poker Table */}
      <View style={styles.table}>
        {/* Table felt */}
        <View style={styles.tableFelt}>
          
          {/* Top players */}
          <View style={styles.topPlayers}>
            <Player {...players[0]} />
            <Player {...players[1]} />
            <Player {...players[2]} />
          </View>
          
          {/* Center area with pot and community cards */}
          <View style={styles.centerArea}>
            <View style={styles.potContainer}>
              <Text style={styles.potLabel}>POT</Text>
              <Text style={styles.potAmount}>{formatPot(potSize)}bb</Text>
            </View>
            
            {/* Community cards area (empty for preflop) */}
            <View style={styles.communityCards}>
              <Text style={styles.communityLabel}>Community Cards</Text>
              <View style={styles.cardSlots}>
                {[1, 2, 3, 4, 5].map((i) => (
                  <View key={i} style={styles.cardSlot} />
                ))}
              </View>
            </View>
          </View>
          
          {/* Bottom players */}
          <View style={styles.bottomPlayers}>
            <Player {...players[3]} />
            <Player {...players[4]} />
          </View>
        </View>
        
        {/* Table rail */}
        <View style={styles.tableRail} />
      </View>
      
      {/* Hero's cards */}
      <View style={styles.heroCardsContainer}>
        <Text style={styles.heroCardsLabel}>Your Cards</Text>
        <View style={styles.heroCards}>
          {heroCards.map((card, index) => (
            <Card key={index} card={card} size="large" style={styles.heroCard} />
          ))}
        </View>
      </View>
      
      {/* Action history */}
      <View style={styles.actionContainer}>
        <Text style={styles.actionText}>{actionHistory}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.primary.dark,
    justifyContent: 'center',
    alignItems: 'center',
  },
  table: {
    width: screenWidth * 0.95,
    height: screenHeight * 0.35,
    position: 'relative',
  },
  tableFelt: {
    flex: 1,
    backgroundColor: Colors.table.felt,
    borderRadius: screenWidth * 0.4,
    padding: Spacing.lg,
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 12,
    borderWidth: 8,
    borderColor: Colors.table.rail,
  },
  tableRail: {
    position: 'absolute',
    top: -4,
    left: -4,
    right: -4,
    bottom: -4,
    borderRadius: screenWidth * 0.4,
    borderWidth: 8,
    borderColor: Colors.table.railDark,
  },
  topPlayers: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  centerArea: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  potContainer: {
    alignItems: 'center',
    backgroundColor: Colors.table.feltDark,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.lg,
    marginBottom: Spacing.lg,
  },
  potLabel: {
    fontSize: Typography.sizes.sm,
    color: Colors.text.secondary,
    fontWeight: Typography.weights.medium,
  },
  potAmount: {
    fontSize: Typography.sizes.xl,
    color: Colors.table.chip,
    fontWeight: Typography.weights.bold,
  },
  communityCards: {
    alignItems: 'center',
  },
  communityLabel: {
    fontSize: Typography.sizes.xs,
    color: Colors.text.muted,
    marginBottom: Spacing.xs,
  },
  cardSlots: {
    flexDirection: 'row',
    gap: Spacing.xs,
  },
  cardSlot: {
    width: 30,
    height: 42,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: Colors.table.feltDark,
    borderStyle: 'dashed',
  },
  bottomPlayers: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  heroCardsContainer: {
    alignItems: 'center',
    marginTop: Spacing.lg,
    backgroundColor: Colors.primary.medium,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.lg,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 6,
  },
  heroCardsLabel: {
    fontSize: Typography.sizes.base,
    color: Colors.text.accent,
    fontWeight: Typography.weights.semibold,
    marginBottom: Spacing.sm,
  },
  heroCards: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  heroCard: {
    marginHorizontal: 0,
  },
  actionContainer: {
    marginTop: Spacing.md,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.sm,
    backgroundColor: Colors.primary.medium,
    borderRadius: BorderRadius.md,
    maxWidth: screenWidth * 0.9,
  },
  actionText: {
    fontSize: Typography.sizes.sm,
    color: Colors.text.primary,
    textAlign: 'center',
    fontWeight: Typography.weights.medium,
  },
});
