import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Modal } from 'react-native';
import { Colors, Typography, Spacing, BorderRadius } from '../../constants/theme';
import { getAvailableScenarios, ScenarioInfo } from '../../utils/scenarioManager';

interface ScenarioSelectorProps {
  currentScenarioId: string;
  onScenarioSelect: (scenarioId: string) => void;
  visible: boolean;
  onClose: () => void;
}

/**
 * ScenarioSelector Component
 * 
 * Modal component that allows users to browse and select different
 * poker hand scenarios. Displays scenario info including difficulty,
 * concept, and description.
 */
export const ScenarioSelector: React.FC<ScenarioSelectorProps> = ({
  currentScenarioId,
  onScenarioSelect,
  visible,
  onClose,
}) => {
  const [scenarios] = useState<ScenarioInfo[]>(getAvailableScenarios());

  /**
   * Get color for difficulty badge
   */
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'BEGINNER':
        return '#4CAF50'; // Green
      case 'INTERMEDIATE':
        return '#FF9800'; // Orange
      case 'ADVANCED':
        return '#F44336'; // Red
      case 'EXPERT':
        return '#9C27B0'; // Purple
      default:
        return Colors.primary.medium;
    }
  };

  /**
   * Handle scenario selection
   */
  const handleScenarioSelect = (scenarioId: string) => {
    onScenarioSelect(scenarioId);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Select Scenario</Text>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>
        </View>

        {/* Scenarios List */}
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {scenarios.map((scenario) => (
            <TouchableOpacity
              key={scenario.id}
              style={[
                styles.scenarioCard,
                currentScenarioId === scenario.id && styles.selectedCard,
              ]}
              onPress={() => handleScenarioSelect(scenario.id)}
            >
              {/* Scenario Header */}
              <View style={styles.scenarioHeader}>
                <View style={styles.titleRow}>
                  <Text style={styles.scenarioTitle} numberOfLines={2}>
                    {scenario.title}
                  </Text>
                  <View
                    style={[
                      styles.difficultyBadge,
                      { backgroundColor: getDifficultyColor(scenario.difficulty) },
                    ]}
                  >
                    <Text style={styles.difficultyText}>
                      {scenario.difficulty}
                    </Text>
                  </View>
                </View>

                {/* Concept */}
                {scenario.concept && (
                  <Text style={styles.conceptText}>{scenario.concept}</Text>
                )}
              </View>

              {/* Description */}
              <Text style={styles.descriptionText} numberOfLines={3}>
                {scenario.description}
              </Text>

              {/* Tags */}
              <View style={styles.tagsContainer}>
                {scenario.tags.slice(0, 3).map((tag, index) => (
                  <View key={index} style={styles.tag}>
                    <Text style={styles.tagText}>{tag}</Text>
                  </View>
                ))}
                {scenario.tags.length > 3 && (
                  <Text style={styles.moreTagsText}>
                    +{scenario.tags.length - 3} more
                  </Text>
                )}
              </View>

              {/* Selected Indicator */}
              {currentScenarioId === scenario.id && (
                <View style={styles.selectedIndicator}>
                  <Text style={styles.selectedText}>✓ Current</Text>
                </View>
              )}
            </TouchableOpacity>
          ))}
        </ScrollView>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            {scenarios.length} scenarios available
          </Text>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  // Main container
  container: {
    flex: 1,
    backgroundColor: Colors.primary.dark,
  },
  
  // Header section
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.primary.medium,
  },
  headerTitle: {
    fontSize: Typography.sizes.xl,
    fontWeight: Typography.weights.bold,
    color: Colors.text.primary,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.primary.medium,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: Typography.sizes.lg,
    color: Colors.text.primary,
    fontWeight: Typography.weights.bold,
  },
  
  // Scroll view
  scrollView: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  
  // Scenario card
  scenarioCard: {
    backgroundColor: Colors.primary.medium,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginVertical: Spacing.sm,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedCard: {
    borderColor: Colors.primary.light,
    backgroundColor: Colors.primary.light + '20',
  },
  
  // Scenario header
  scenarioHeader: {
    marginBottom: Spacing.md,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.xs,
  },
  scenarioTitle: {
    fontSize: Typography.sizes.lg,
    fontWeight: Typography.weights.bold,
    color: Colors.text.primary,
    flex: 1,
    marginRight: Spacing.sm,
  },
  
  // Difficulty badge
  difficultyBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
  },
  difficultyText: {
    fontSize: Typography.sizes.xs,
    fontWeight: Typography.weights.bold,
    color: Colors.text.primary,
  },
  
  // Concept text
  conceptText: {
    fontSize: Typography.sizes.sm,
    fontStyle: 'italic',
    color: Colors.text.secondary,
    marginBottom: Spacing.xs,
  },
  
  // Description
  descriptionText: {
    fontSize: Typography.sizes.base,
    color: Colors.text.primary,
    lineHeight: 20,
    marginBottom: Spacing.md,
  },
  
  // Tags
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  tag: {
    backgroundColor: Colors.primary.dark,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
    marginRight: Spacing.xs,
    marginBottom: Spacing.xs,
  },
  tagText: {
    fontSize: Typography.sizes.xs,
    color: Colors.text.secondary,
  },
  moreTagsText: {
    fontSize: Typography.sizes.xs,
    color: Colors.text.secondary,
    fontStyle: 'italic',
  },
  
  // Selected indicator
  selectedIndicator: {
    position: 'absolute',
    top: Spacing.sm,
    right: Spacing.sm,
    backgroundColor: Colors.primary.light,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
  },
  selectedText: {
    fontSize: Typography.sizes.xs,
    fontWeight: Typography.weights.bold,
    color: Colors.text.primary,
  },
  
  // Footer
  footer: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.primary.medium,
    alignItems: 'center',
  },
  footerText: {
    fontSize: Typography.sizes.sm,
    color: Colors.text.secondary,
  },
});
