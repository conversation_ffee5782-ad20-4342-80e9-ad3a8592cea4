import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import { Colors, Typography, Spacing } from '../../constants/theme';

interface ActionStep {
  position: string;
  action: 'FOLD' | 'CALL' | 'RAISE' | 'CHECK';
  amount?: number;
  delay: number;
}

interface ActionSequenceProps {
  steps: ActionStep[];
  onComplete: () => void;
}

export const ActionSequence: React.FC<ActionSequenceProps> = ({
  steps,
  onComplete,
}) => {
  const [currentStep, setCurrentStep] = useState(-1);
  const [isComplete, setIsComplete] = useState(false);
  const fadeAnim = new Animated.Value(0);

  useEffect(() => {
    if (steps.length === 0) {
      onComplete();
      return;
    }

    const runSequence = async () => {
      for (let i = 0; i < steps.length; i++) {
        await new Promise(resolve => {
          setTimeout(() => {
            setCurrentStep(i);
            
            // Fade in animation
            Animated.sequence([
              Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 300,
                useNativeDriver: true,
              }),
              Animated.delay(1200),
              Animated.timing(fadeAnim, {
                toValue: 0,
                duration: 300,
                useNativeDriver: true,
              }),
            ]).start(() => {
              resolve(undefined);
            });
          }, steps[i].delay);
        });
      }
      
      setIsComplete(true);
      setTimeout(() => {
        onComplete();
      }, 500);
    };

    runSequence();
  }, [steps]);

  if (isComplete || currentStep === -1) {
    return null;
  }

  const currentAction = steps[currentStep];
  
  const getActionColor = (action: string) => {
    switch (action) {
      case 'FOLD':
        return Colors.actions.fold.primary;
      case 'CALL':
      case 'CHECK':
        return Colors.actions.call.primary;
      case 'RAISE':
        return Colors.actions.raise.primary;
      default:
        return Colors.text.primary;
    }
  };

  const getActionText = (step: ActionStep) => {
    switch (step.action) {
      case 'FOLD':
        return `${step.position} folds`;
      case 'CALL':
        return `${step.position} calls${step.amount ? ` ${step.amount}bb` : ''}`;
      case 'RAISE':
        return `${step.position} raises to ${step.amount}bb`;
      case 'CHECK':
        return `${step.position} checks`;
      default:
        return `${step.position} ${step.action.toLowerCase()}`;
    }
  };

  return (
    <View style={styles.overlay}>
      <Animated.View 
        style={[
          styles.actionContainer,
          { 
            opacity: fadeAnim,
            borderColor: getActionColor(currentAction.action),
          }
        ]}
      >
        <Text style={[styles.actionText, { color: getActionColor(currentAction.action) }]}>
          {getActionText(currentAction)}
        </Text>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    zIndex: 1000,
  },
  actionContainer: {
    backgroundColor: Colors.primary.medium,
    paddingHorizontal: Spacing.xl,
    paddingVertical: Spacing.lg,
    borderRadius: 12,
    borderWidth: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  actionText: {
    fontSize: Typography.sizes.lg,
    fontWeight: Typography.weights.bold,
    textAlign: 'center',
  },
});
