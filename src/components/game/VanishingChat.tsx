import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Animated, Dimensions } from 'react-native';
import { Colors, Typography, Spacing, BorderRadius } from '../../constants/theme';

const { height: screenHeight } = Dimensions.get('window');

interface ChatMessage {
  id: string;
  position: string;
  action: 'FOLD' | 'CALL' | 'RAISE' | 'CHECK';
  amount?: number;
  delay: number;
}

interface VanishingChatProps {
  messages: ChatMessage[];
  onComplete: () => void;
}

/**
 * VanishingChat Component
 * 
 * Displays a sequence of poker action messages that appear and vanish
 * in a chat-like format during the betting animation sequence.
 * Each message fades in, stays visible, then fades out before the next one appears.
 */
export const VanishingChat: React.FC<VanishingChatProps> = ({
  messages,
  onComplete,
}) => {
  // State to track which message is currently being displayed
  const [currentMessageIndex, setCurrentMessageIndex] = useState(-1);
  const [isComplete, setIsComplete] = useState(false);

  // Animated value for fade in/out effects - use useRef to persist across renders
  const fadeAnim = React.useRef(new Animated.Value(0)).current;

  useEffect(() => {
    console.log('VanishingChat: Starting with messages:', messages.length);

    // Reset state
    setCurrentMessageIndex(-1);
    setIsComplete(false);
    fadeAnim.setValue(0);

    // If no messages provided, complete immediately
    if (messages.length === 0) {
      console.log('VanishingChat: No messages, completing immediately');
      onComplete();
      return;
    }

    // Function to display messages sequentially
    const runMessageSequence = async () => {
      console.log('VanishingChat: Starting message sequence');

      for (let i = 0; i < messages.length; i++) {
        console.log(`VanishingChat: Showing message ${i + 1}/${messages.length}:`, messages[i]);

        // Reset animation value for each message
        fadeAnim.setValue(0);

        // Show the current message
        setCurrentMessageIndex(i);

        // Wait for the delay before starting this message
        await new Promise(resolve => setTimeout(resolve, messages[i].delay));

        // Animate the message: fade in -> stay -> fade out (slower timing)
        await new Promise(resolve => {
          Animated.sequence([
            // Fade in (slower)
            Animated.timing(fadeAnim, {
              toValue: 1,
              duration: 600, // Increased from 400
              useNativeDriver: true,
            }),
            // Stay visible (much longer)
            Animated.delay(2000), // Increased from 1200
            // Fade out (slower)
            Animated.timing(fadeAnim, {
              toValue: 0,
              duration: 600, // Increased from 400
              useNativeDriver: true,
            }),
          ]).start(() => {
            console.log(`VanishingChat: Message ${i + 1} animation complete`);
            resolve(undefined);
          });
        });
      }

      console.log('VanishingChat: All messages complete, notifying parent');

      // Mark sequence as complete and notify parent
      setIsComplete(true);
      setTimeout(() => {
        onComplete();
      }, 300);
    };

    runMessageSequence();
  }, [messages, onComplete, fadeAnim]);

  // Don't render anything if sequence is complete or no current message
  if (isComplete || currentMessageIndex === -1) {
    return null;
  }

  const currentMessage = messages[currentMessageIndex];
  
  /**
   * Get appropriate color for each action type
   */
  const getActionColor = (action: string) => {
    switch (action) {
      case 'FOLD':
        return Colors.actions.fold.primary;
      case 'CALL':
      case 'CHECK':
        return Colors.actions.call.primary;
      case 'RAISE':
        return Colors.actions.raise.primary;
      default:
        return Colors.text.primary;
    }
  };

  /**
   * Format the action message text
   */
  const getMessageText = (message: ChatMessage) => {
    switch (message.action) {
      case 'FOLD':
        return `${message.position} folds`;
      case 'CALL':
        return `${message.position} calls${message.amount ? ` ${message.amount}bb` : ''}`;
      case 'RAISE':
        return `${message.position} raises ${message.amount}bb`;
      case 'CHECK':
        return `${message.position} checks`;
      default:
        return `${message.position} ${message.action.toLowerCase()}`;
    }
  };

  return (
    <View style={styles.chatContainer}>
      {/* Semi-transparent background overlay for better visibility */}
      <Animated.View
        style={[
          styles.backgroundOverlay,
          { opacity: fadeAnim }
        ]}
      />

      {/* Message bubble */}
      <Animated.View
        style={[
          styles.messageContainer,
          {
            opacity: fadeAnim,
            borderLeftColor: getActionColor(currentMessage.action),
          }
        ]}
      >
        <Text style={[styles.messageText, { color: getActionColor(currentMessage.action) }]}>
          {getMessageText(currentMessage)}
        </Text>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  // Container positioned within game area for better mobile visibility
  chatContainer: {
    position: 'absolute',
    top: 60, // Fixed position within game area
    left: Spacing.lg,
    right: Spacing.lg,
    zIndex: 1000,
    alignItems: 'center',
  },
  // Semi-transparent background for better visibility
  backgroundOverlay: {
    position: 'absolute',
    top: -10,
    left: -20,
    right: -20,
    bottom: -10,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: BorderRadius.lg,
  },
  // Individual message bubble with chat-like styling - mobile optimized
  messageContainer: {
    backgroundColor: Colors.primary.medium,
    paddingHorizontal: Spacing.md, // Reduced padding for mobile
    paddingVertical: Spacing.sm,   // Reduced padding for mobile
    borderRadius: BorderRadius.md, // Smaller radius for mobile
    borderLeftWidth: 3,            // Thinner border for mobile
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,            // Lighter shadow for mobile
    shadowRadius: 2,               // Smaller shadow for mobile
    elevation: 3,                  // Lower elevation for mobile
    maxWidth: '85%',               // Slightly wider for mobile
    minWidth: 120,                 // Minimum width for readability
  },
  // Message text styling - mobile optimized
  messageText: {
    fontSize: Typography.sizes.sm, // Smaller text for mobile
    fontWeight: Typography.weights.semibold,
    textAlign: 'center',
    lineHeight: 18,                // Better line height for mobile
  },
});
