import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import { Colors, Typography, Spacing, BorderRadius } from '../../constants/theme';

interface ChatMessage {
  id: string;
  position: string;
  action: 'FOLD' | 'CALL' | 'RAISE' | 'CHECK';
  amount?: number;
  delay: number;
}

interface VanishingChatProps {
  messages: ChatMessage[];
  onComplete: () => void;
}

/**
 * VanishingChat Component
 * 
 * Displays a sequence of poker action messages that appear and vanish
 * in a chat-like format during the betting animation sequence.
 * Each message fades in, stays visible, then fades out before the next one appears.
 */
export const VanishingChat: React.FC<VanishingChatProps> = ({
  messages,
  onComplete,
}) => {
  // State to track which message is currently being displayed
  const [currentMessageIndex, setCurrentMessageIndex] = useState(-1);
  const [isComplete, setIsComplete] = useState(false);

  // Animated value for fade in/out effects - use useRef to persist across renders
  const fadeAnim = React.useRef(new Animated.Value(0)).current;

  useEffect(() => {
    console.log('VanishingChat: Starting with messages:', messages.length);

    // Reset state
    setCurrentMessageIndex(-1);
    setIsComplete(false);
    fadeAnim.setValue(0);

    // If no messages provided, complete immediately
    if (messages.length === 0) {
      console.log('VanishingChat: No messages, completing immediately');
      onComplete();
      return;
    }

    // Function to display messages sequentially
    const runMessageSequence = async () => {
      console.log('VanishingChat: Starting message sequence');

      for (let i = 0; i < messages.length; i++) {
        console.log(`VanishingChat: Showing message ${i + 1}/${messages.length}:`, messages[i]);

        // Reset animation value for each message
        fadeAnim.setValue(0);

        // Show the current message
        setCurrentMessageIndex(i);

        // Wait for the delay before starting this message
        await new Promise(resolve => setTimeout(resolve, messages[i].delay));

        // Animate the message: fade in -> stay -> fade out
        await new Promise(resolve => {
          Animated.sequence([
            // Fade in
            Animated.timing(fadeAnim, {
              toValue: 1,
              duration: 400,
              useNativeDriver: true,
            }),
            // Stay visible
            Animated.delay(1200),
            // Fade out
            Animated.timing(fadeAnim, {
              toValue: 0,
              duration: 400,
              useNativeDriver: true,
            }),
          ]).start(() => {
            console.log(`VanishingChat: Message ${i + 1} animation complete`);
            resolve(undefined);
          });
        });
      }

      console.log('VanishingChat: All messages complete, notifying parent');

      // Mark sequence as complete and notify parent
      setIsComplete(true);
      setTimeout(() => {
        onComplete();
      }, 300);
    };

    runMessageSequence();
  }, [messages, onComplete, fadeAnim]);

  // Don't render anything if sequence is complete or no current message
  if (isComplete || currentMessageIndex === -1) {
    return null;
  }

  const currentMessage = messages[currentMessageIndex];
  
  /**
   * Get appropriate color for each action type
   */
  const getActionColor = (action: string) => {
    switch (action) {
      case 'FOLD':
        return Colors.actions.fold.primary;
      case 'CALL':
      case 'CHECK':
        return Colors.actions.call.primary;
      case 'RAISE':
        return Colors.actions.raise.primary;
      default:
        return Colors.text.primary;
    }
  };

  /**
   * Format the action message text
   */
  const getMessageText = (message: ChatMessage) => {
    switch (message.action) {
      case 'FOLD':
        return `${message.position} folds`;
      case 'CALL':
        return `${message.position} calls${message.amount ? ` ${message.amount}bb` : ''}`;
      case 'RAISE':
        return `${message.position} raises ${message.amount}bb`;
      case 'CHECK':
        return `${message.position} checks`;
      default:
        return `${message.position} ${message.action.toLowerCase()}`;
    }
  };

  return (
    <View style={styles.chatContainer}>
      <Animated.View 
        style={[
          styles.messageContainer,
          { 
            opacity: fadeAnim,
            borderLeftColor: getActionColor(currentMessage.action),
          }
        ]}
      >
        <Text style={[styles.messageText, { color: getActionColor(currentMessage.action) }]}>
          {getMessageText(currentMessage)}
        </Text>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  // Container positioned at top of screen for chat messages
  chatContainer: {
    position: 'absolute',
    top: 100,
    left: Spacing.lg,
    right: Spacing.lg,
    zIndex: 1000,
    alignItems: 'center',
  },
  // Individual message bubble with chat-like styling
  messageContainer: {
    backgroundColor: Colors.primary.medium,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.lg,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
    maxWidth: '80%',
  },
  // Message text styling
  messageText: {
    fontSize: Typography.sizes.base,
    fontWeight: Typography.weights.semibold,
    textAlign: 'center',
  },
});
