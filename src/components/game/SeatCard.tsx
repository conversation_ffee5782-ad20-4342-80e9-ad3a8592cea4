import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Colors, Typography, Spacing, BorderRadius } from '../../constants/theme';

interface SeatCardProps {
  position: string;
  stack: number;
  isActive?: boolean;
  isHero?: boolean;
  onPress?: () => void;
  style?: any;
}

export const SeatCard: React.FC<SeatCardProps> = ({
  position,
  stack,
  isActive = false,
  isHero = false,
  onPress,
  style,
}) => {
  const formatStack = (amount: number) => {
    if (amount >= 1000) {
      return `${(amount / 1000).toFixed(1)}K`;
    }
    return amount.toString();
  };

  const getBorderStyle = () => {
    if (isActive) {
      return {
        borderWidth: 3,
        borderColor: '#FFD700', // Gold glow for active player
        shadowColor: '#FFD700',
        shadowOffset: { width: 0, height: 0 },
        shadowOpacity: 0.6,
        shadowRadius: 8,
        elevation: 8,
      };
    }
    if (isHero) {
      return {
        borderWidth: 2,
        borderColor: Colors.primary.light,
      };
    }
    return {
      borderWidth: 1,
      borderColor: Colors.table.feltDark,
    };
  };

  return (
    <TouchableOpacity
      style={[styles.touchTarget, style]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <View style={[styles.seatCard, getBorderStyle()]}>
        {/* Position label */}
        <Text style={styles.positionLabel}>{position}</Text>
        
        {/* Stack size */}
        <Text style={styles.stackSize}>{formatStack(stack)}</Text>
        
        {/* Chip icon */}
        <View style={styles.chipIcon}>
          <View style={[styles.chip, styles.chipBottom]} />
          <View style={[styles.chip, styles.chipMiddle]} />
          <View style={[styles.chip, styles.chipTop]} />
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  touchTarget: {
    width: 48,
    height: 64,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
  },
  seatCard: {
    width: 44,
    height: 56,
    backgroundColor: '#2E3440', // Dark grey as specified
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: Spacing.xs,
  },
  positionLabel: {
    fontSize: 12,
    fontWeight: '600', // SF-Pro Semibold equivalent
    color: '#F4F5F6',
    textAlign: 'center',
    marginBottom: 2,
  },
  stackSize: {
    fontSize: 11,
    fontWeight: '500', // Medium
    color: '#9AA4B0',
    textAlign: 'center',
    marginBottom: 4,
  },
  chipIcon: {
    width: 20,
    height: 20,
    position: 'relative',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  chip: {
    width: 16,
    height: 4,
    borderRadius: 2,
    position: 'absolute',
  },
  chipBottom: {
    backgroundColor: '#1E90FF', // Blue chip
    bottom: 0,
  },
  chipMiddle: {
    backgroundColor: '#FF2D55', // Red chip
    bottom: 3,
  },
  chipTop: {
    backgroundColor: '#1E90FF', // Blue chip
    bottom: 6,
  },
});
