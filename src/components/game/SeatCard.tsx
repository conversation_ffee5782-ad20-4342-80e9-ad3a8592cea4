import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Colors, Typography, Spacing, BorderRadius } from '../../constants/theme';
import { PlayerActionAnimation } from './PlayerActionAnimation';

interface SeatCardProps {
  position: string;
  stack: number;
  bigBlind?: number;
  isActive?: boolean;
  isHero?: boolean;
  onPress?: () => void;
  style?: any;
  // Animation props
  showAnimation?: boolean;
  animationAction?: 'FOLD' | 'CALL' | 'RAISE' | 'CHECK' | 'BET' | 'ALL_IN';
  animationAmount?: number;
  onAnimationComplete?: () => void;
}

/**
 * SeatCard Component
 *
 * Displays a poker seat with player position, stack size in BB,
 * and visual indicators for active player and hero.
 * Includes chip stack visualization and proper touch targets.
 */
export const SeatCard: React.FC<SeatCardProps> = ({
  position,
  stack,
  bigBlind = 50, // Default big blind value
  isActive = false,
  isHero = false,
  onPress,
  style,
  // Animation props
  showAnimation = false,
  animationAction,
  animationAmount,
  onAnimationComplete,
}) => {
  /**
   * Format stack size to show in Big Blinds (BB)
   * Converts chip count to BB and formats for display
   */
  const formatStackInBB = (amount: number, bb: number) => {
    const stackInBB = Math.floor(amount / bb);
    if (stackInBB >= 100) {
      return `${Math.floor(stackInBB / 10) * 10}BB`; // Round to nearest 10 for large stacks
    }
    return `${stackInBB}BB`;
  };

  /**
   * Get border styling based on player state
   * Active players get gold glow, hero gets accent border, others get default
   */
  const getBorderStyle = () => {
    if (isActive) {
      // Active player gets gold glow effect
      return {
        borderWidth: 3,
        borderColor: '#FFD700', // Gold glow for active player
        shadowColor: '#FFD700',
        shadowOffset: { width: 0, height: 0 },
        shadowOpacity: 0.6,
        shadowRadius: 8,
        elevation: 8,
      };
    }
    if (isHero) {
      // Hero player gets accent color border
      return {
        borderWidth: 2,
        borderColor: Colors.primary.light,
      };
    }
    // Default border for other players
    return {
      borderWidth: 1,
      borderColor: Colors.table.feltDark,
    };
  };

  return (
    <TouchableOpacity
      style={[styles.touchTarget, style]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <View style={[styles.seatCard, getBorderStyle()]}>
        <Text style={styles.positionLabel}>{position}</Text>
        <Text style={styles.stackSize}>{formatStackInBB(stack, bigBlind)}</Text>
        <View style={styles.chipIcon}>
          <View style={[styles.chip, styles.chipBottom]} />
          <View style={[styles.chip, styles.chipMiddle]} />
          <View style={[styles.chip, styles.chipTop]} />
        </View>

        {/* Player Action Animation Overlay */}
        {showAnimation && animationAction && (
          <PlayerActionAnimation
            playerId={position}
            position={position}
            action={animationAction}
            amount={animationAmount}
            isActive={showAnimation}
            onComplete={onAnimationComplete}
          />
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  touchTarget: {
    width: 40,
    height: 52,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
  },
  seatCard: {
    width: 38,
    height: 50,
    backgroundColor: '#2E3440', // Dark grey as specified
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 2,
  },
  positionLabel: {
    fontSize: 10,
    fontWeight: '600', // SF-Pro Semibold equivalent
    color: '#F4F5F6',
    textAlign: 'center',
    marginBottom: 1,
  },
  stackSize: {
    fontSize: 9,
    fontWeight: '500', // Medium
    color: '#9AA4B0',
    textAlign: 'center',
    marginBottom: 2,
  },
  chipIcon: {
    width: 16,
    height: 16,
    position: 'relative',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  chip: {
    width: 12,
    height: 3,
    borderRadius: 1.5,
    position: 'absolute',
  },
  chipBottom: {
    backgroundColor: '#1E90FF', // Blue chip
    bottom: 0,
  },
  chipMiddle: {
    backgroundColor: '#FF2D55', // Red chip
    bottom: 2,
  },
  chipTop: {
    backgroundColor: '#1E90FF', // Blue chip
    bottom: 4,
  },
});
