import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, SafeAreaView, TouchableOpacity } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { ActionButton } from '../components/common/ActionButton';
import { PokerTable } from '../components/game/PokerTable';
import { ActionSequence } from '../components/game/ActionSequence';
import { VanishingChat } from '../components/game/VanishingChat';
import { PokerScenario, Action } from '../types';
import { Colors, Typography, Spacing, BorderRadius } from '../constants/theme';
import { SettingsProvider, useSettings } from '../contexts/SettingsContext';

// Sample data - will be replaced with real data from store/API
const sampleScenario: PokerScenario = {
  id: '1',
  gameType: 'NLHE',
  street: 'PREFLOP',
  holeCards: ['A♠', 'K♦'],
  position: 'Button',
  stackSize: 100,
  potSize: 7.5,
  effectiveStacks: 100,
  actionHistory: 'UTG raises to 2.5bb, MP calls',
  tags: ['3bet', 'position'],
  difficulty: 'INTERMEDIATE',
  createdAt: new Date(),
};

/**
 * GameScreenContent Component
 *
 * Main game screen that displays the poker table, player cards,
 * action buttons, and handles the betting sequence animation.
 * Includes vanishing chat messages and action sequence display.
 */
const GameScreenContent: React.FC = () => {
  const { cardColorScheme, setCardColorScheme } = useSettings();

  // State management for animation sequences
  const [showActionSequence, setShowActionSequence] = useState(false);
  const [showVanishingChat, setShowVanishingChat] = useState(false);
  const [showDecisionButtons, setShowDecisionButtons] = useState(true);

  // State for dynamic pot size during animation
  const [currentPotSize, setCurrentPotSize] = useState(sampleScenario.potSize);

  // State for player animations
  const [currentPlayerAnimation, setCurrentPlayerAnimation] = useState<{
    position: string;
    action: 'FOLD' | 'CALL' | 'RAISE' | 'CHECK' | 'BET' | 'ALL_IN';
    amount?: number;
  } | null>(null);

  // Chat messages for the vanishing chat animation with slower, more realistic delays
  const chatMessages = [
    { id: '1', position: 'UTG', action: 'RAISE' as const, amount: 2.5, delay: 0 },
    { id: '2', position: 'MP', action: 'CALL' as const, amount: 2.5, delay: 1000 }, // Increased from 500
    { id: '3', position: 'CO', action: 'FOLD' as const, delay: 2000 }, // Increased from 1000
  ];

  // Action sequence for the overlay animation (kept for compatibility)
  const actionSequence = [
    { position: 'UTG', action: 'RAISE' as const, amount: 2.5, delay: 0 },
    { position: 'MP', action: 'CALL' as const, amount: 2.5, delay: 1500 },
    { position: 'CO', action: 'FOLD' as const, delay: 3000 },
  ];

  const handleAction = (action: Action, betSize?: number) => {
    console.log(`Player chose: ${action}`, betSize ? `with bet size: ${betSize}bb` : '');
    // TODO: Handle the action and show consensus data
  };

  const toggleColorScheme = () => {
    setCardColorScheme(cardColorScheme === 'traditional' ? 'fourColor' : 'traditional');
  };

  /**
   * Handle completion of action sequence animation
   * Shows decision buttons after animation completes
   */
  const handleSequenceComplete = () => {
    setShowActionSequence(false);
    setShowDecisionButtons(true);
  };

  /**
   * Handle completion of vanishing chat sequence
   * Shows decision buttons after chat messages finish
   */
  const handleChatComplete = () => {
    console.log('GameScreen: Chat sequence completed');
    setShowVanishingChat(false);
    setCurrentPlayerAnimation(null); // Clear any remaining animations
    setShowDecisionButtons(true);
  };

  /**
   * Handle player action during animation sequence
   * Triggers visual animation on the corresponding player
   */
  const handlePlayerAction = (position: string, action: string, amount?: number) => {
    console.log(`GameScreen: Player action - ${position} ${action}${amount ? ` ${amount}bb` : ''}`);
    setCurrentPlayerAnimation({
      position,
      action: action as 'FOLD' | 'CALL' | 'RAISE' | 'CHECK' | 'BET' | 'ALL_IN',
      amount,
    });
  };

  /**
   * Handle completion of player animation
   * Clears the current animation state
   */
  const handlePlayerAnimationComplete = () => {
    console.log('GameScreen: Player animation completed');
    setCurrentPlayerAnimation(null);
  };

  /**
   * Start the animation sequence
   * Shows vanishing chat messages and updates pot
   */
  const startAnimation = () => {
    console.log('GameScreen: Starting animation sequence');
    setShowVanishingChat(true);
    setShowDecisionButtons(false);

    // Reset pot to initial value
    setCurrentPotSize(sampleScenario.potSize);

    // Update pot as actions happen (adjusted timing for slower animation)
    setTimeout(() => {
      console.log('GameScreen: UTG raises, updating pot');
      setCurrentPotSize(prev => prev + 125); // UTG raises 2.5bb (125 chips)
    }, 1500); // Increased from 1000

    setTimeout(() => {
      console.log('GameScreen: MP calls, updating pot');
      setCurrentPotSize(prev => prev + 125); // MP calls 2.5bb (125 chips)
    }, 4500); // Increased from 2500
  };

  useEffect(() => {
    // Don't auto-start animation, wait for user to trigger it
    setShowActionSequence(false);
    setShowVanishingChat(false);
    setShowDecisionButtons(true);
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" />

      {/* Compact Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={styles.appTitle}>SnapFold</Text>
          <Text style={styles.handCounter}>Hand 7/20</Text>
        </View>

        <View style={styles.headerRight}>
          <TouchableOpacity onPress={toggleColorScheme} style={styles.colorButton}>
            <Text style={styles.colorButtonText}>
              {cardColorScheme === 'traditional' ? '2-Color' : '4-Color'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Main game area with table and chat overlay */}
      <View style={styles.gameArea}>
        {/* Poker Table with smaller dimensions */}
        <PokerTable
          heroCards={sampleScenario.holeCards}
          heroPosition={sampleScenario.position}
          potSize={currentPotSize} // Use dynamic pot size that updates during animation
          actionHistory={sampleScenario.actionHistory}
          bigBlind={50} // Pass big blind value for BB calculations
          currentAnimation={currentPlayerAnimation} // Pass current player animation
          onAnimationComplete={handlePlayerAnimationComplete} // Handle animation completion
        />

        {/* Vanishing Chat Messages Animation - positioned relative to game area */}
        {showVanishingChat && (
          <VanishingChat
            messages={chatMessages}
            onComplete={handleChatComplete}
            onPlayerAction={handlePlayerAction} // Connect player action handler
          />
        )}
      </View>

      {/* Action Sequence Animation (overlay style - kept as backup) */}
      {showActionSequence && (
        <ActionSequence
          steps={actionSequence}
          onComplete={handleSequenceComplete}
        />
      )}

      {/* Decision Buttons - only show after animation */}
      {showDecisionButtons && (
        <View style={styles.buttonsContainer}>
          {/* Animation trigger button */}
          <TouchableOpacity
            style={styles.animationButton}
            onPress={startAnimation}
          >
            <Text style={styles.animationButtonText}>▶ Show Action Sequence</Text>
          </TouchableOpacity>

          <Text style={styles.decisionPrompt}>What's your move?</Text>

          <View style={styles.buttonRow}>
            <ActionButton
              action="FOLD"
              onPress={handleAction}
              style={styles.actionButton}
            />

            <ActionButton
              action="CALL"
              onPress={handleAction}
              style={styles.actionButton}
            />

            <ActionButton
              action="RAISE"
              onPress={handleAction}
              style={styles.actionButton}
            />
          </View>
        </View>
      )}
    </SafeAreaView>
  );
};

export const GameScreen: React.FC = () => {
  return (
    <SettingsProvider>
      <GameScreenContent />
    </SettingsProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.primary.dark,
  },
  gameArea: {
    flex: 1,
    position: 'relative',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: Spacing.lg,
    paddingHorizontal: Spacing.xl,
    backgroundColor: Colors.primary.medium,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },
  appTitle: {
    fontSize: Typography.sizes['2xl'],
    fontWeight: Typography.weights.bold,
    color: Colors.text.primary,
    letterSpacing: 0.5,
  },
  handCounter: {
    fontSize: Typography.sizes.sm,
    color: Colors.text.secondary,
    fontWeight: Typography.weights.medium,
  },
  colorButton: {
    backgroundColor: Colors.primary.accent,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.md,
  },
  colorButtonText: {
    fontSize: Typography.sizes.xs,
    color: Colors.text.primary,
    fontWeight: Typography.weights.semibold,
  },

  buttonsContainer: {
    padding: Spacing.lg,
    paddingBottom: Spacing['3xl'],
    backgroundColor: Colors.primary.medium,
    borderTopWidth: 1,
    borderTopColor: Colors.primary.light,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  decisionPrompt: {
    fontSize: Typography.sizes.base,
    fontWeight: Typography.weights.bold,
    color: Colors.text.primary,
    textAlign: 'center',
    marginBottom: Spacing.lg,
    letterSpacing: 0.5,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: Spacing.md,
    paddingHorizontal: Spacing.xs,
  },
  actionButton: {
    marginHorizontal: 0,
  },
  animationButton: {
    backgroundColor: Colors.primary.light,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.md,
    marginBottom: Spacing.md,
    alignSelf: 'center',
  },
  animationButtonText: {
    color: Colors.text.primary,
    fontSize: Typography.sizes.sm,
    fontWeight: Typography.weights.semibold,
    textAlign: 'center',
  },
});
