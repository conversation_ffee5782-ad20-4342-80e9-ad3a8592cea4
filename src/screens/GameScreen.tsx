import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, SafeAreaView, TouchableOpacity } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { ActionButton } from '../components/common/ActionButton';
import { PokerTable } from '../components/game/PokerTable';
import { ActionSequence } from '../components/game/ActionSequence';
import { PokerScenario, Action } from '../types';
import { Colors, Typography, Spacing, BorderRadius } from '../constants/theme';
import { SettingsProvider, useSettings } from '../contexts/SettingsContext';

// Sample data - will be replaced with real data from store/API
const sampleScenario: PokerScenario = {
  id: '1',
  gameType: 'NLHE',
  street: 'PREFLOP',
  holeCards: ['A♠', 'K♦'],
  position: 'Button',
  stackSize: 100,
  potSize: 7.5,
  effectiveStacks: 100,
  actionHistory: 'UTG raises to 2.5bb, MP calls',
  tags: ['3bet', 'position'],
  difficulty: 'INTERMEDIATE',
  createdAt: new Date(),
};

const GameScreenContent: React.FC = () => {
  const { cardColorScheme, setCardColorScheme } = useSettings();
  const [showActionSequence, setShowActionSequence] = useState(true);
  const [showDecisionButtons, setShowDecisionButtons] = useState(false);

  // Action sequence that leads to the player's decision
  const actionSequence = [
    { position: 'UTG', action: 'RAISE' as const, amount: 2.5, delay: 0 },
    { position: 'MP', action: 'CALL' as const, amount: 2.5, delay: 1500 },
    { position: 'CO', action: 'FOLD' as const, delay: 3000 },
  ];

  const handleAction = (action: Action, betSize?: number) => {
    console.log(`Player chose: ${action}`, betSize ? `with bet size: ${betSize}bb` : '');
    // TODO: Handle the action and show consensus data
  };

  const toggleColorScheme = () => {
    setCardColorScheme(cardColorScheme === 'traditional' ? 'fourColor' : 'traditional');
  };

  const handleSequenceComplete = () => {
    setShowActionSequence(false);
    setShowDecisionButtons(true);
  };

  useEffect(() => {
    // Reset animation state when component mounts
    setShowActionSequence(true);
    setShowDecisionButtons(false);
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" />

      {/* Compact Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={styles.appTitle}>SnapFold</Text>
          <Text style={styles.handCounter}>Hand 7/20</Text>
        </View>

        <View style={styles.headerRight}>
          <TouchableOpacity onPress={toggleColorScheme} style={styles.colorButton}>
            <Text style={styles.colorButtonText}>
              {cardColorScheme === 'traditional' ? '2-Color' : '4-Color'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Poker Table */}
      <PokerTable
        heroCards={sampleScenario.holeCards}
        heroPosition={sampleScenario.position}
        potSize={sampleScenario.potSize}
        actionHistory={sampleScenario.actionHistory}
      />

      {/* Action Sequence Animation */}
      {showActionSequence && (
        <ActionSequence
          steps={actionSequence}
          onComplete={handleSequenceComplete}
        />
      )}

      {/* Decision Buttons - only show after animation */}
      {showDecisionButtons && (
        <View style={styles.buttonsContainer}>
          <Text style={styles.decisionPrompt}>What's your move?</Text>

          <View style={styles.buttonRow}>
            <ActionButton
              action="FOLD"
              onPress={handleAction}
              style={styles.actionButton}
            />

            <ActionButton
              action="CALL"
              onPress={handleAction}
              style={styles.actionButton}
            />

            <ActionButton
              action="RAISE"
              onPress={handleAction}
              style={styles.actionButton}
            />
          </View>
        </View>
      )}
    </SafeAreaView>
  );
};

export const GameScreen: React.FC = () => {
  return (
    <SettingsProvider>
      <GameScreenContent />
    </SettingsProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.primary.dark,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: Spacing.lg,
    paddingHorizontal: Spacing.xl,
    backgroundColor: Colors.primary.medium,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },
  appTitle: {
    fontSize: Typography.sizes['2xl'],
    fontWeight: Typography.weights.bold,
    color: Colors.text.primary,
    letterSpacing: 0.5,
  },
  handCounter: {
    fontSize: Typography.sizes.sm,
    color: Colors.text.secondary,
    fontWeight: Typography.weights.medium,
  },
  colorButton: {
    backgroundColor: Colors.primary.accent,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.md,
  },
  colorButtonText: {
    fontSize: Typography.sizes.xs,
    color: Colors.text.primary,
    fontWeight: Typography.weights.semibold,
  },

  buttonsContainer: {
    padding: Spacing.lg,
    paddingBottom: Spacing['3xl'],
    backgroundColor: Colors.primary.medium,
    borderTopWidth: 1,
    borderTopColor: Colors.primary.light,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  decisionPrompt: {
    fontSize: Typography.sizes.base,
    fontWeight: Typography.weights.bold,
    color: Colors.text.primary,
    textAlign: 'center',
    marginBottom: Spacing.lg,
    letterSpacing: 0.5,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: Spacing.md,
    paddingHorizontal: Spacing.xs,
  },
  actionButton: {
    marginHorizontal: 0,
  },
});
