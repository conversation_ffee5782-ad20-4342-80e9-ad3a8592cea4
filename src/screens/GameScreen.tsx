import React from 'react';
import { View, Text, StyleSheet, SafeAreaView } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Card } from '../components/common/Card';
import { ActionButton } from '../components/common/ActionButton';
import { PokerScenario, Action } from '../types';
import { Colors, Typography, Spacing, BorderRadius } from '../constants/theme';

// Sample data - will be replaced with real data from store/API
const sampleScenario: PokerScenario = {
  id: '1',
  gameType: 'NLHE',
  street: 'PREFLOP',
  holeCards: ['A♠', 'K♦'],
  position: 'Button',
  stackSize: 100,
  potSize: 7.5,
  effectiveStacks: 100,
  actionHistory: 'UTG raises to 2.5bb, MP calls',
  tags: ['3bet', 'position'],
  difficulty: 'INTERMEDIATE',
  createdAt: new Date(),
};

export const GameScreen: React.FC = () => {
  const handleAction = (action: Action, betSize?: number) => {
    console.log(`Player chose: ${action}`, betSize ? `with bet size: ${betSize}bb` : '');
    // TODO: Handle the action and show consensus data
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" />
      
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.appTitle}>SnapFold</Text>
        <Text style={styles.subtitle}>Poker Training</Text>
      </View>

      {/* Scenario Info */}
      <View style={styles.scenarioContainer}>
        <Text style={styles.sectionTitle}>
          Your Position: {sampleScenario.position}
        </Text>
        
        {/* Hole Cards */}
        <View style={styles.cardsContainer}>
          <Text style={styles.cardsLabel}>Your Cards:</Text>
          <View style={styles.cardsRow}>
            {sampleScenario.holeCards.map((card, index) => (
              <Card key={index} card={card} size="medium" />
            ))}
          </View>
        </View>

        {/* Game Info */}
        <View style={styles.gameInfo}>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Stack Size:</Text>
            <Text style={styles.infoValue}>{sampleScenario.stackSize}bb</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Pot Size:</Text>
            <Text style={styles.infoValue}>{sampleScenario.potSize}bb</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Game Type:</Text>
            <Text style={styles.infoValue}>{sampleScenario.gameType}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Street:</Text>
            <Text style={styles.infoValue}>{sampleScenario.street}</Text>
          </View>
        </View>

        {/* Action History */}
        <View style={styles.actionHistory}>
          <Text style={styles.actionLabel}>Action:</Text>
          <Text style={styles.actionText}>{sampleScenario.actionHistory}</Text>
        </View>
      </View>

      {/* Decision Buttons */}
      <View style={styles.buttonsContainer}>
        <Text style={styles.decisionPrompt}>What's your move?</Text>
        
        <View style={styles.buttonRow}>
          <ActionButton 
            action="FOLD" 
            onPress={handleAction}
            style={styles.actionButton}
          />
          
          <ActionButton 
            action="CALL" 
            onPress={handleAction}
            style={styles.actionButton}
          />
          
          <ActionButton 
            action="RAISE" 
            onPress={handleAction}
            style={styles.actionButton}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.primary.dark,
  },
  header: {
    alignItems: 'center',
    paddingVertical: Spacing.xl,
    borderBottomWidth: 1,
    borderBottomColor: Colors.primary.light,
  },
  appTitle: {
    fontSize: Typography.sizes['3xl'],
    fontWeight: Typography.weights.bold,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
  },
  subtitle: {
    fontSize: Typography.sizes.base,
    color: Colors.text.secondary,
  },
  scenarioContainer: {
    flex: 1,
    padding: Spacing.xl,
  },
  sectionTitle: {
    fontSize: Typography.sizes.lg,
    fontWeight: Typography.weights.semibold,
    color: Colors.text.primary,
    marginBottom: Spacing.xl,
    textAlign: 'center',
  },
  cardsContainer: {
    alignItems: 'center',
    marginBottom: Spacing['3xl'],
  },
  cardsLabel: {
    fontSize: Typography.sizes.base,
    color: Colors.text.secondary,
    marginBottom: Spacing.md,
  },
  cardsRow: {
    flexDirection: 'row',
    gap: Spacing.lg,
  },
  gameInfo: {
    backgroundColor: Colors.primary.medium,
    borderRadius: BorderRadius.lg,
    padding: Spacing.xl,
    marginBottom: Spacing.xl,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Spacing.md,
  },
  infoLabel: {
    fontSize: Typography.sizes.base,
    color: Colors.text.secondary,
  },
  infoValue: {
    fontSize: Typography.sizes.base,
    fontWeight: Typography.weights.semibold,
    color: Colors.text.primary,
  },
  actionHistory: {
    backgroundColor: Colors.primary.medium,
    borderRadius: BorderRadius.lg,
    padding: Spacing.xl,
  },
  actionLabel: {
    fontSize: Typography.sizes.base,
    color: Colors.text.secondary,
    marginBottom: Spacing.sm,
  },
  actionText: {
    fontSize: Typography.sizes.base,
    color: Colors.text.primary,
    lineHeight: Typography.lineHeights.relaxed * Typography.sizes.base,
  },
  buttonsContainer: {
    padding: Spacing.xl,
    paddingBottom: Spacing['4xl'],
  },
  decisionPrompt: {
    fontSize: Typography.sizes.lg,
    fontWeight: Typography.weights.semibold,
    color: Colors.text.primary,
    textAlign: 'center',
    marginBottom: Spacing.xl,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  actionButton: {
    marginHorizontal: 0,
  },
});
