import React from 'react';
import { View, Text, StyleSheet, SafeAreaView } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Card } from '../components/common/Card';
import { ActionButton } from '../components/common/ActionButton';
import { PokerScenario, Action } from '../types';
import { Colors, Typography, Spacing, BorderRadius } from '../constants/theme';

// Sample data - will be replaced with real data from store/API
const sampleScenario: PokerScenario = {
  id: '1',
  gameType: 'NLHE',
  street: 'PREFLOP',
  holeCards: ['A♠', 'K♦'],
  position: 'Button',
  stackSize: 100,
  potSize: 7.5,
  effectiveStacks: 100,
  actionHistory: 'UTG raises to 2.5bb, MP calls',
  tags: ['3bet', 'position'],
  difficulty: 'INTERMEDIATE',
  createdAt: new Date(),
};

export const GameScreen: React.FC = () => {
  const handleAction = (action: Action, betSize?: number) => {
    console.log(`Player chose: ${action}`, betSize ? `with bet size: ${betSize}bb` : '');
    // TODO: Handle the action and show consensus data
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" />
      
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.appTitle}>SnapFold</Text>
        <Text style={styles.subtitle}>Poker Training</Text>

        {/* Progress indicator */}
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View style={[styles.progressFill, { width: '35%' }]} />
          </View>
          <Text style={styles.progressText}>Hand 7 of 20</Text>
        </View>
      </View>

      {/* Scenario Info */}
      <View style={styles.scenarioContainer}>
        <View style={styles.scenarioHeader}>
          <Text style={styles.sectionTitle}>
            Your Position: {sampleScenario.position}
          </Text>
          <View style={styles.difficultyBadge}>
            <Text style={styles.difficultyText}>{sampleScenario.difficulty}</Text>
          </View>
        </View>
        
        {/* Hole Cards */}
        <View style={styles.cardsContainer}>
          <Text style={styles.cardsLabel}>Your Cards:</Text>
          <View style={styles.cardsRow}>
            {sampleScenario.holeCards.map((card, index) => (
              <Card key={index} card={card} size="medium" />
            ))}
          </View>
        </View>

        {/* Game Info */}
        <View style={styles.gameInfo}>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Stack Size:</Text>
            <Text style={styles.infoValue}>{sampleScenario.stackSize}bb</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Pot Size:</Text>
            <Text style={styles.infoValue}>{sampleScenario.potSize}bb</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Game Type:</Text>
            <Text style={styles.infoValue}>{sampleScenario.gameType}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Street:</Text>
            <Text style={styles.infoValue}>{sampleScenario.street}</Text>
          </View>
        </View>

        {/* Action History */}
        <View style={styles.actionHistory}>
          <Text style={styles.actionLabel}>Action:</Text>
          <Text style={styles.actionText}>{sampleScenario.actionHistory}</Text>
        </View>
      </View>

      {/* Decision Buttons */}
      <View style={styles.buttonsContainer}>
        <Text style={styles.decisionPrompt}>What's your move?</Text>
        
        <View style={styles.buttonRow}>
          <ActionButton 
            action="FOLD" 
            onPress={handleAction}
            style={styles.actionButton}
          />
          
          <ActionButton 
            action="CALL" 
            onPress={handleAction}
            style={styles.actionButton}
          />
          
          <ActionButton 
            action="RAISE" 
            onPress={handleAction}
            style={styles.actionButton}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.primary.dark,
  },
  header: {
    alignItems: 'center',
    paddingVertical: Spacing['2xl'],
    paddingHorizontal: Spacing.xl,
    borderBottomWidth: 1,
    borderBottomColor: Colors.primary.light,
    backgroundColor: Colors.primary.medium,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  appTitle: {
    fontSize: Typography.sizes['4xl'],
    fontWeight: Typography.weights.bold,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
    letterSpacing: 1,
  },
  subtitle: {
    fontSize: Typography.sizes.lg,
    color: Colors.text.accent,
    fontWeight: Typography.weights.medium,
  },
  progressContainer: {
    marginTop: Spacing.lg,
    alignItems: 'center',
    width: '100%',
  },
  progressBar: {
    width: '60%',
    height: 4,
    backgroundColor: Colors.primary.light,
    borderRadius: BorderRadius.sm,
    overflow: 'hidden',
    marginBottom: Spacing.xs,
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.text.accent,
    borderRadius: BorderRadius.sm,
  },
  progressText: {
    fontSize: Typography.sizes.sm,
    color: Colors.text.secondary,
    fontWeight: Typography.weights.medium,
  },
  scenarioContainer: {
    flex: 1,
    padding: Spacing.xl,
    paddingTop: Spacing['2xl'],
  },
  scenarioHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing['2xl'],
  },
  sectionTitle: {
    fontSize: Typography.sizes.xl,
    fontWeight: Typography.weights.bold,
    color: Colors.text.primary,
    letterSpacing: 0.5,
    flex: 1,
  },
  difficultyBadge: {
    backgroundColor: Colors.status.warning,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.full,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  difficultyText: {
    fontSize: Typography.sizes.xs,
    fontWeight: Typography.weights.bold,
    color: Colors.primary.dark,
    letterSpacing: 0.5,
  },
  cardsContainer: {
    alignItems: 'center',
    marginBottom: Spacing['4xl'],
    backgroundColor: Colors.primary.medium,
    borderRadius: BorderRadius.xl,
    padding: Spacing['2xl'],
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 12,
  },
  cardsLabel: {
    fontSize: Typography.sizes.lg,
    color: Colors.text.accent,
    marginBottom: Spacing.lg,
    fontWeight: Typography.weights.semibold,
  },
  cardsRow: {
    flexDirection: 'row',
    gap: Spacing.xl,
  },
  gameInfo: {
    backgroundColor: Colors.primary.medium,
    borderRadius: BorderRadius.xl,
    padding: Spacing['2xl'],
    marginBottom: Spacing.xl,
    borderWidth: 1,
    borderColor: Colors.primary.light,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 6,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.lg,
    paddingVertical: Spacing.xs,
  },
  infoLabel: {
    fontSize: Typography.sizes.lg,
    color: Colors.text.secondary,
    fontWeight: Typography.weights.medium,
  },
  infoValue: {
    fontSize: Typography.sizes.lg,
    fontWeight: Typography.weights.bold,
    color: Colors.text.primary,
    backgroundColor: Colors.primary.accent,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.md,
  },
  actionHistory: {
    backgroundColor: Colors.primary.medium,
    borderRadius: BorderRadius.xl,
    padding: Spacing['2xl'],
    borderWidth: 1,
    borderColor: Colors.primary.light,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 6,
  },
  actionLabel: {
    fontSize: Typography.sizes.lg,
    color: Colors.text.accent,
    marginBottom: Spacing.md,
    fontWeight: Typography.weights.semibold,
  },
  actionText: {
    fontSize: Typography.sizes.lg,
    color: Colors.text.primary,
    lineHeight: Typography.lineHeights.relaxed * Typography.sizes.lg,
    fontWeight: Typography.weights.medium,
  },
  buttonsContainer: {
    padding: Spacing.xl,
    paddingBottom: Spacing['5xl'],
    backgroundColor: Colors.primary.medium,
    borderTopWidth: 1,
    borderTopColor: Colors.primary.light,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 12,
  },
  decisionPrompt: {
    fontSize: Typography.sizes.xl,
    fontWeight: Typography.weights.bold,
    color: Colors.text.primary,
    textAlign: 'center',
    marginBottom: Spacing['2xl'],
    letterSpacing: 0.5,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: Spacing.lg,
    paddingHorizontal: Spacing.sm,
  },
  actionButton: {
    marginHorizontal: 0,
  },
});
