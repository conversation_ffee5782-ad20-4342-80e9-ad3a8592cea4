import React, { createContext, useContext, useState, ReactNode } from 'react';

export type CardColorScheme = 'traditional' | 'fourColor';

interface SettingsContextType {
  cardColorScheme: CardColorScheme;
  setCardColorScheme: (scheme: CardColorScheme) => void;
}

const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

interface SettingsProviderProps {
  children: ReactNode;
}

export const SettingsProvider: React.FC<SettingsProviderProps> = ({ children }) => {
  const [cardColorScheme, setCardColorScheme] = useState<CardColorScheme>('traditional');

  return (
    <SettingsContext.Provider value={{
      cardColorScheme,
      setCardColorScheme,
    }}>
      {children}
    </SettingsContext.Provider>
  );
};

export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (context === undefined) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};
