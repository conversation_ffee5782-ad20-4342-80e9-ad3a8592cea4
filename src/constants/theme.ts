// Color palette
export const Colors = {
  // Primary poker table colors with gradients
  primary: {
    dark: '#0a0f1c',      // Deeper dark background
    medium: '#1a2332',    // Enhanced medium background
    light: '#2d3748',     // Lighter accent
    accent: '#4a5568',    // New accent color
  },

  // Gradient definitions
  gradients: {
    background: ['#0a0f1c', '#1a2332'], // Main background gradient
    card: ['#ffffff', '#f7fafc'],       // Card gradient
    button: {
      fold: ['#dc2626', '#b91c1c'],     // Red gradient
      call: ['#2563eb', '#1d4ed8'],     // Blue gradient
      raise: ['#059669', '#047857'],    // Green gradient
    },
  },

  // Text colors
  text: {
    primary: '#ffffff',   // Pure white for main text
    secondary: '#a0aec0', // Softer gray
    muted: '#718096',     // Muted text
    accent: '#63b3ed',    // Accent text color
  },

  // Enhanced action button colors
  actions: {
    fold: {
      primary: '#dc2626',
      secondary: '#b91c1c',
      light: '#fecaca',
    },
    call: {
      primary: '#2563eb',
      secondary: '#1d4ed8',
      light: '#bfdbfe',
    },
    raise: {
      primary: '#059669',
      secondary: '#047857',
      light: '#a7f3d0',
    },
    disabled: '#4a5568',
  },

  // Status colors
  status: {
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',
  },

  // Enhanced card colors
  cards: {
    background: '#ffffff',
    backgroundSecondary: '#f7fafc',
    text: '#1a202c',
    red: '#e53e3e',       // Brighter red for hearts/diamonds
    black: '#1a202c',     // Deep black for spades/clubs
    border: '#e2e8f0',    // Subtle border
  },

  // Poker table colors
  table: {
    felt: '#0d4f3c',      // Poker table green
    rail: '#8b4513',      // Wood rail color
    chip: '#ffd700',      // Gold chip color
  },

  // Consensus visualization colors
  consensus: {
    fold: '#fca5a5',
    call: '#93c5fd',
    raise: '#86efac',
    background: '#f8fafc',
  },
} as const;

// Typography
export const Typography = {
  sizes: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 28,
    '4xl': 32,
  },
  
  weights: {
    normal: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
  },
  
  lineHeights: {
    tight: 1.2,
    normal: 1.5,
    relaxed: 1.75,
  },
} as const;

// Spacing
export const Spacing = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  '2xl': 24,
  '3xl': 32,
  '4xl': 40,
  '5xl': 48,
} as const;

// Border radius
export const BorderRadius = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  full: 9999,
} as const;

// Enhanced shadows
export const Shadows = {
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 6,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 12,
  },
  xl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 16,
  },
  // Special shadows for cards
  card: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
  // Glow effect for buttons
  glow: {
    shadowColor: '#4299e1',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 0,
  },
} as const;

// Layout
export const Layout = {
  window: {
    width: 375,  // iPhone standard width for reference
    height: 812, // iPhone standard height for reference
  },
  
  card: {
    width: 60,
    height: 85,
    aspectRatio: 60 / 85,
  },
  
  button: {
    height: 48,
    minWidth: 100,
  },
} as const;

// Animation durations
export const Animations = {
  fast: 150,
  normal: 300,
  slow: 500,
} as const;

// Poker-specific constants
export const PokerConstants = {
  positions: ['UTG', 'UTG+1', 'MP', 'MP+1', 'CO', 'Button', 'SB', 'BB'] as const,
  
  suits: {
    spades: '♠',
    hearts: '♥',
    diamonds: '♦',
    clubs: '♣',
  },
  
  ranks: ['2', '3', '4', '5', '6', '7', '8', '9', 'T', 'J', 'Q', 'K', 'A'] as const,
  
  actions: ['FOLD', 'CHECK', 'CALL', 'BET', 'RAISE', 'ALL_IN'] as const,
  
  gameTypes: ['NLHE', 'PLO', 'PLO5', 'STUD'] as const,
  
  streets: ['PREFLOP', 'FLOP', 'TURN', 'RIVER'] as const,
} as const;

// Level progression
export const LevelSystem = {
  levels: [
    { level: 1, name: 'Fish', xpRequired: 0, color: '#94a3b8' },
    { level: 2, name: 'Recreational', xpRequired: 100, color: '#10b981' },
    { level: 3, name: 'Regular', xpRequired: 300, color: '#3b82f6' },
    { level: 4, name: 'Solid', xpRequired: 600, color: '#8b5cf6' },
    { level: 5, name: 'Strong', xpRequired: 1000, color: '#f59e0b' },
    { level: 6, name: 'Expert', xpRequired: 1500, color: '#ef4444' },
    { level: 7, name: 'Pro', xpRequired: 2500, color: '#ec4899' },
    { level: 8, name: 'Elite', xpRequired: 4000, color: '#06b6d4' },
    { level: 9, name: 'Legend', xpRequired: 6000, color: '#84cc16' },
    { level: 10, name: 'GOAT', xpRequired: 10000, color: '#fbbf24' },
  ],
  
  xpRewards: {
    decision: 10,
    consensusMatch: 5,
    streak: 2,
    dailyChallenge: 50,
    weeklyChallenge: 200,
  },
} as const;
