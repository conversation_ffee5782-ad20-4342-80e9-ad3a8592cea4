// Color palette
export const Colors = {
  // Primary poker table colors
  primary: {
    dark: '#0f172a',      // Dark poker table background
    medium: '#1e293b',    // Card backgrounds, info panels
    light: '#334155',     // Borders, dividers
  },
  
  // Text colors
  text: {
    primary: '#f1f5f9',   // Main text (white-ish)
    secondary: '#94a3b8', // Secondary text (gray)
    muted: '#64748b',     // Muted text
  },
  
  // Action button colors
  actions: {
    fold: '#dc2626',      // Red for fold
    call: '#2563eb',      // Blue for call/check
    raise: '#16a34a',     // Green for bet/raise
    disabled: '#475569',  // Gray for disabled
  },
  
  // Status colors
  status: {
    success: '#10b981',   // Green for success
    warning: '#f59e0b',   // Yellow for warning
    error: '#ef4444',     // Red for error
    info: '#3b82f6',      // Blue for info
  },
  
  // Card colors
  cards: {
    background: '#ffffff',
    text: '#1e293b',
    red: '#dc2626',       // Hearts and diamonds
    black: '#1e293b',     // Spades and clubs
  },
  
  // Consensus visualization colors
  consensus: {
    fold: '#fca5a5',      // Light red
    call: '#93c5fd',      // Light blue
    raise: '#86efac',     // Light green
    background: '#f8fafc',
  },
} as const;

// Typography
export const Typography = {
  sizes: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 28,
    '4xl': 32,
  },
  
  weights: {
    normal: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
  },
  
  lineHeights: {
    tight: 1.2,
    normal: 1.5,
    relaxed: 1.75,
  },
} as const;

// Spacing
export const Spacing = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  '2xl': 24,
  '3xl': 32,
  '4xl': 40,
  '5xl': 48,
} as const;

// Border radius
export const BorderRadius = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  full: 9999,
} as const;

// Shadows
export const Shadows = {
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
} as const;

// Layout
export const Layout = {
  window: {
    width: 375,  // iPhone standard width for reference
    height: 812, // iPhone standard height for reference
  },
  
  card: {
    width: 60,
    height: 85,
    aspectRatio: 60 / 85,
  },
  
  button: {
    height: 48,
    minWidth: 100,
  },
} as const;

// Animation durations
export const Animations = {
  fast: 150,
  normal: 300,
  slow: 500,
} as const;

// Poker-specific constants
export const PokerConstants = {
  positions: ['UTG', 'UTG+1', 'MP', 'MP+1', 'CO', 'Button', 'SB', 'BB'] as const,
  
  suits: {
    spades: '♠',
    hearts: '♥',
    diamonds: '♦',
    clubs: '♣',
  },
  
  ranks: ['2', '3', '4', '5', '6', '7', '8', '9', 'T', 'J', 'Q', 'K', 'A'] as const,
  
  actions: ['FOLD', 'CHECK', 'CALL', 'BET', 'RAISE', 'ALL_IN'] as const,
  
  gameTypes: ['NLHE', 'PLO', 'PLO5', 'STUD'] as const,
  
  streets: ['PREFLOP', 'FLOP', 'TURN', 'RIVER'] as const,
} as const;

// Level progression
export const LevelSystem = {
  levels: [
    { level: 1, name: 'Fish', xpRequired: 0, color: '#94a3b8' },
    { level: 2, name: 'Recreational', xpRequired: 100, color: '#10b981' },
    { level: 3, name: 'Regular', xpRequired: 300, color: '#3b82f6' },
    { level: 4, name: 'Solid', xpRequired: 600, color: '#8b5cf6' },
    { level: 5, name: 'Strong', xpRequired: 1000, color: '#f59e0b' },
    { level: 6, name: 'Expert', xpRequired: 1500, color: '#ef4444' },
    { level: 7, name: 'Pro', xpRequired: 2500, color: '#ec4899' },
    { level: 8, name: 'Elite', xpRequired: 4000, color: '#06b6d4' },
    { level: 9, name: 'Legend', xpRequired: 6000, color: '#84cc16' },
    { level: 10, name: 'GOAT', xpRequired: 10000, color: '#fbbf24' },
  ],
  
  xpRewards: {
    decision: 10,
    consensusMatch: 5,
    streak: 2,
    dailyChallenge: 50,
    weeklyChallenge: 200,
  },
} as const;
