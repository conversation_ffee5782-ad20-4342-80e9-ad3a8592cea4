// Core poker types
export type Card = string; // e.g., 'A♠', 'K♦', 'Q♣', 'J♥'
export type Position = 'UTG' | 'UTG+1' | 'MP' | 'MP+1' | 'CO' | 'Button' | 'SB' | 'BB';
export type Action = 'FOLD' | 'CHECK' | 'CALL' | 'BET' | 'RAISE' | 'ALL_IN';
export type GameType = 'NLHE' | 'PLO' | 'PLO5' | 'STUD';
export type Street = 'PREFLOP' | 'FLOP' | 'TURN' | 'RIVER';

// Poker scenario types
export interface PokerScenario {
  id: string;
  gameType: GameType;
  street: Street;
  holeCards: Card[];
  position: Position;
  stackSize: number; // in big blinds
  potSize: number; // in big blinds
  effectiveStacks: number; // in big blinds
  actionHistory: string;
  boardCards?: Card[]; // for post-flop scenarios
  tags: string[]; // e.g., ['3bet', 'multiway', 'deep-stacks']
  difficulty: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED';
  createdAt: Date;
}

// User decision types
export interface UserDecision {
  id: string;
  userId: string;
  scenarioId: string;
  action: Action;
  betSize?: number; // in big blinds, for BET/RAISE actions
  timestamp: Date;
  timeSpent: number; // in milliseconds
}

// Consensus data types
export interface ConsensusData {
  scenarioId: string;
  totalDecisions: number;
  actionBreakdown: {
    [key in Action]: {
      count: number;
      percentage: number;
      averageBetSize?: number; // for BET/RAISE actions
    };
  };
  lastUpdated: Date;
}

// User types
export interface User {
  id: string;
  email?: string;
  displayName?: string;
  isGuest: boolean;
  level: number;
  xp: number;
  archetype?: PlayerArchetype;
  stats: UserStats;
  createdAt: Date;
  lastActiveAt: Date;
}

export interface UserStats {
  totalDecisions: number;
  consensusScore: number; // percentage of decisions that match majority
  averageDecisionTime: number; // in milliseconds
  streakCurrent: number;
  streakBest: number;
  scenariosCompleted: number;
  favoritePosition?: Position;
  strongestStreet?: Street;
  weakestStreet?: Street;
}

export type PlayerArchetype = 
  | 'TIGHT_AGGRESSIVE' 
  | 'LOOSE_AGGRESSIVE' 
  | 'TIGHT_PASSIVE' 
  | 'LOOSE_PASSIVE' 
  | 'BALANCED';

// Gamification types
export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  requirement: number;
  category: 'DECISIONS' | 'CONSENSUS' | 'STREAK' | 'SPECIAL';
  unlockedAt?: Date;
}

export interface Challenge {
  id: string;
  name: string;
  description: string;
  type: 'DAILY' | 'WEEKLY' | 'MONTHLY';
  requirement: {
    action: string;
    target: number;
  };
  reward: {
    xp: number;
    badge?: string;
  };
  expiresAt: Date;
  completedAt?: Date;
}

// App state types
export interface GameState {
  currentScenario: PokerScenario | null;
  userDecision: UserDecision | null;
  consensusData: ConsensusData | null;
  isLoading: boolean;
  error: string | null;
}

export interface UserState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  achievements: Achievement[];
  activeChallenges: Challenge[];
}

// API response types
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
}

// Navigation types (for React Navigation)
export type RootStackParamList = {
  Home: undefined;
  Game: { scenarioId?: string };
  Results: { scenarioId: string; userDecision: UserDecision };
  Profile: undefined;
  Settings: undefined;
  Onboarding: undefined;
};

// Component prop types
export interface CardProps {
  card: Card;
  size?: 'small' | 'medium' | 'large';
  style?: any;
}

export interface ActionButtonProps {
  action: Action;
  onPress: (action: Action, betSize?: number) => void;
  disabled?: boolean;
  style?: any;
}
