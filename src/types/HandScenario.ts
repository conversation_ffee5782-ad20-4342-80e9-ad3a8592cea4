/**
 * Comprehensive Hand Scenario Types
 * 
 * This file defines the complete structure for poker hand scenarios,
 * including all game state, player information, animation details,
 * and decision points needed to recreate any poker situation.
 */

export type Suit = 'hearts' | 'diamonds' | 'clubs' | 'spades';
export type Rank = '2' | '3' | '4' | '5' | '6' | '7' | '8' | '9' | 'T' | 'J' | 'Q' | 'K' | 'A';
export type Position = 'UTG' | 'UTG+1' | 'MP' | 'MP+1' | 'CO' | 'BTN' | 'SB' | 'BB';
export type ActionType = 'FOLD' | 'CHECK' | 'CALL' | 'RAISE' | 'BET' | 'ALL_IN';
export type Street = 'PREFLOP' | 'FLOP' | 'TURN' | 'RIVER';
export type GameType = 'CASH' | 'TOURNAMENT' | 'SIT_AND_GO';
export type Difficulty = 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT';

/**
 * Represents a single playing card
 */
export interface Card {
  rank: Rank;
  suit: Suit;
  // For display purposes
  displayRank?: string; // e.g., "A", "K", "Q", "J", "10"
  displaySuit?: string; // e.g., "♠", "♥", "♦", "♣"
}

/**
 * Represents a player's action in the hand
 */
export interface PlayerAction {
  playerId: string;
  position: Position;
  action: ActionType;
  amount?: number; // In chips
  amountBB?: number; // In big blinds
  timestamp: number; // Milliseconds from hand start
  isHero?: boolean;
  // Animation details
  animationDelay?: number; // Delay before showing this action
  showInChat?: boolean; // Whether to show in vanishing chat
  chatMessage?: string; // Custom chat message override
  // Visual animation details
  highlightPlayer?: boolean; // Whether to highlight the acting player
  animationType?: 'FOLD' | 'CALL' | 'RAISE' | 'CHECK' | 'BET' | 'ALL_IN'; // Animation type
  animationDuration?: number; // How long the player animation lasts
}

/**
 * Represents a player at the table
 */
export interface Player {
  id: string;
  name: string;
  position: Position;
  stackSize: number; // In chips
  stackSizeBB: number; // In big blinds
  isHero: boolean;
  isActive: boolean; // Currently in the hand
  isActing: boolean; // Currently making a decision
  // Player stats (for advanced scenarios)
  vpip?: number; // Voluntarily put money in pot %
  pfr?: number; // Pre-flop raise %
  aggression?: number; // Aggression factor
  // Visual state
  hasCards: boolean;
  showCards?: Card[]; // If cards are revealed
  seatNumber: number; // 1-9 for positioning
}

/**
 * Represents the current pot state
 */
export interface PotState {
  mainPot: number; // In chips
  mainPotBB: number; // In big blinds
  sidePots?: Array<{
    amount: number;
    amountBB: number;
    eligiblePlayers: string[]; // Player IDs
  }>;
  totalPot: number; // Sum of all pots
  totalPotBB: number; // In big blinds
}

/**
 * Represents the board state (community cards)
 */
export interface BoardState {
  flop?: [Card, Card, Card];
  turn?: Card;
  river?: Card;
  // Helper getters
  allCards: Card[]; // All revealed community cards
  street: Street; // Current street based on revealed cards
}

/**
 * Represents game settings and blinds
 */
export interface GameSettings {
  gameType: GameType;
  smallBlind: number; // In chips
  bigBlind: number; // In chips
  ante?: number; // In chips (for tournaments)
  // Tournament specific
  level?: number;
  blindsIncreaseTime?: number; // Minutes
  // Cash game specific
  minBuyIn?: number;
  maxBuyIn?: number;
  // Table settings
  maxPlayers: number; // Usually 6 or 9
  currentPlayers: number;
}

/**
 * Represents the complete action sequence for animation
 */
export interface ActionSequence {
  street: Street;
  actions: PlayerAction[];
  // Animation settings
  autoPlay: boolean; // Whether to auto-start animation
  showChat: boolean; // Whether to show vanishing chat
  showOverlay: boolean; // Whether to show action overlay
  // Timing settings
  defaultActionDelay: number; // Default delay between actions (ms)
  messageDisplayTime: number; // How long each message shows (ms)
  fadeInDuration: number; // Fade in animation duration (ms)
  fadeOutDuration: number; // Fade out animation duration (ms)
}

/**
 * Represents the decision point for the hero
 */
export interface DecisionPoint {
  // Current situation
  facingAction: PlayerAction | null; // The action hero is facing
  legalActions: ActionType[]; // What actions are available
  // Betting options
  minRaise?: number; // Minimum raise amount in chips
  maxRaise?: number; // Maximum raise amount (stack size)
  callAmount?: number; // Amount needed to call
  potOdds?: number; // Pot odds as decimal (e.g., 0.33 for 3:1)
  // Decision context
  timeToAct?: number; // Seconds to make decision (for tournaments)
  previousActions: PlayerAction[]; // All actions this street
  // Educational context
  concept?: string; // Main concept being taught
  difficulty: Difficulty;
  tags: string[]; // e.g., ['3bet', 'position', 'bluff-catcher']
}

/**
 * Complete hand scenario structure
 */
export interface HandScenario {
  // Basic hand information
  id: string;
  title: string;
  description: string;
  difficulty: Difficulty;
  tags: string[];
  concept?: string; // Main educational concept
  
  // Game setup
  gameSettings: GameSettings;
  players: Player[];
  heroPosition: Position;
  
  // Hand state
  heroCards: [Card, Card];
  board: BoardState;
  currentStreet: Street;
  
  // Pot and betting
  potState: PotState;
  currentBet: number; // Current bet to call (in chips)
  currentBetBB: number; // Current bet to call (in BB)
  
  // Action history
  actionHistory: {
    preflop: PlayerAction[];
    flop?: PlayerAction[];
    turn?: PlayerAction[];
    river?: PlayerAction[];
  };
  
  // Current decision point
  decisionPoint: DecisionPoint;
  
  // Animation configuration
  animationSequence: ActionSequence;
  
  // Educational content
  analysis?: {
    correctAction: ActionType;
    correctAmount?: number; // If raising/betting
    explanation: string;
    alternativeLines?: Array<{
      action: ActionType;
      amount?: number;
      explanation: string;
      rating: 'GOOD' | 'OKAY' | 'BAD';
    }>;
  };
  
  // Metadata
  createdAt: Date;
  updatedAt: Date;
  author?: string;
  source?: string; // e.g., "Hand History", "Training", "Generated"
  handNumber?: string; // Original hand number if from real play
}

/**
 * Template for creating new hand scenarios
 */
export interface HandScenarioTemplate {
  // Required fields for generation
  gameType: GameType;
  stakes: string; // e.g., "1/2", "25/50", "Tournament Level 5"
  heroPosition: Position;
  heroCards: [Card, Card];
  
  // Scenario parameters
  concept: string;
  difficulty: Difficulty;
  street: Street;
  
  // Player configuration
  tableSize: 6 | 9;
  activePlayerCount: number;
  stackSizes: 'DEEP' | 'MEDIUM' | 'SHORT'; // Relative to blinds
  
  // Action parameters
  preflop: {
    raiseCount: number; // Number of raises preflop
    callers: number; // Number of callers
    heroFacing: ActionType; // What action hero faces
  };
  
  // Board texture (if post-flop)
  boardTexture?: 'DRY' | 'WET' | 'COORDINATED' | 'RAINBOW';
  
  // Generation settings
  randomizeStacks: boolean;
  randomizePositions: boolean;
  includeStats: boolean; // Whether to include player stats
}
