/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 * @format
 */

'use strict';

import NativeAnimatedHelper from '../NativeAnimatedHelper';
import type { PlatformConfig } from '../AnimatedPlatformConfig';
import type AnimatedValue from '../nodes/AnimatedValue';
export type EndResult = {
  finished: boolean,
  ...
};
export type EndCallback = (result: EndResult) => void;
export type AnimationConfig = {
  isInteraction?: boolean,
  useNativeDriver: boolean,
  platformConfig?: PlatformConfig,
  onComplete?: ?EndCallback,
  iterations?: number,
};
let startNativeAnimationNextId = 1;

// Important note: start() and stop() will only be called at most once.
// Once an animation has been stopped or finished its course, it will
// not be reused.
declare class Animation {
  __active: boolean,
  __isInteraction: boolean,
  __nativeId: number,
  __onEnd: ?EndCallback,
  __iterations: number,
  start(fromValue: number, onUpdate: (value: number) => void, onEnd: ?EndCallback, previousAnimation: ?Animation, animatedValue: AnimatedValue): void,
  stop(): void,
  __getNativeAnimationConfig(): any,
  __debouncedOnEnd(result: EndResult): void,
  __startNativeAnimation(animatedValue: AnimatedValue): void,
}
export default Animation;