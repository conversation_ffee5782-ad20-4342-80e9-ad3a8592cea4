/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 * @format
 */

'use strict';

import type AnimatedNode from './AnimatedNode';
import AnimatedInterpolation from './AnimatedInterpolation';
import AnimatedWithChildren from './AnimatedWithChildren';
import type { InterpolationConfigType } from './AnimatedInterpolation';
import type { PlatformConfig } from '../AnimatedPlatformConfig';
declare class AnimatedDiffClamp extends AnimatedWithChildren {
  _a: AnimatedNode,
  _min: number,
  _max: number,
  _value: number,
  _lastValue: number,
  constructor(a: AnimatedNode, min: number, max: number): any,
  __makeNative(platformConfig: ?PlatformConfig): any,
  interpolate<OutputT: number | string>(config: InterpolationConfigType<OutputT>): AnimatedInterpolation<OutputT>,
  __getValue(): number,
  __attach(): void,
  __detach(): void,
  __getNativeConfig(): any,
}
export default AnimatedDiffClamp;