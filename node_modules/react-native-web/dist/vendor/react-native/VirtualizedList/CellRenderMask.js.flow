/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict
 * @format
 */

import invariant from 'fbjs/lib/invariant';
export type CellRegion = {
  first: number,
  last: number,
  isSpacer: boolean,
};
declare export class CellRenderMask {
  _numCells: number,
  _regions: Array<CellRegion>,
  constructor(numCells: number): any,
  enumerateRegions(): $ReadOnlyArray<CellRegion>,
  addCells(cells: {
    first: number,
    last: number,
  }): void,
  numCells(): number,
  equals(other: CellRenderMask): boolean,
  _findRegion(cellIdx: number): [CellRegion, number],
}