{"_from": "@types/react@~19.0.10", "_id": "@types/react@19.0.14", "_inBundle": false, "_integrity": "sha512-ixLZ7zG7j1fM0DijL9hDArwhwcCb4vqmePgwtV0GfnkHRSCUEv4LvzarcTdhoqgyMznUx/EhoTUv31CKZzkQlw==", "_location": "/@types/react", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/react@~19.0.10", "name": "@types/react", "escapedName": "@types%2freact", "scope": "@types", "rawSpec": "~19.0.10", "saveSpec": null, "fetchSpec": "~19.0.10"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/@types/react/-/react-19.0.14.tgz", "_shasum": "f2f62035290afd755095cb6644e10b599db72f4e", "_spec": "@types/react@~19.0.10", "_where": "/Users/<USER>/projects/SnapFold", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://asana.com"}, {"name": "AssureSign", "url": "http://www.assuresign.com"}, {"name": "Microsoft", "url": "https://microsoft.com"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/bbenezech"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pza<PERSON><PERSON>ky"}, {"name": "<PERSON>", "url": "https://github.com/ericanderson"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as"}, {"name": "<PERSON>", "url": "https://github.com/theruther4d"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/guil<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ferdaber"}, {"name": "<PERSON>", "url": "https://github.com/jrakotoharisoa"}, {"name": "<PERSON>", "url": "https://github.com/pascaloliv"}, {"name": "<PERSON>", "url": "https://github.com/hotell"}, {"name": "<PERSON>", "url": "https://github.com/franklixuefei"}, {"name": "<PERSON>", "url": "https://github.com/Jessidhia"}, {"name": "Saransh Kataria", "url": "https://github.com/saranshkataria"}, {"name": "Kanitkorn Sujautra", "url": "https://github.com/lukyth"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/zieka"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/dancerphil"}, {"name": "<PERSON>", "url": "https://github.com/dimitrop<PERSON>los"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/disjukr"}, {"name": "<PERSON>", "url": "https://github.com/vhfmag"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/priyanshurav"}, {"name": "<PERSON>", "url": "https://github.com/Semigradsky"}, {"name": "<PERSON>", "url": "https://github.com/mattpocock"}], "dependencies": {"csstype": "^3.0.2"}, "deprecated": false, "description": "TypeScript definitions for react", "exports": {".": {"types@<=5.0": {"default": "./ts5.0/index.d.ts"}, "types": {"default": "./index.d.ts"}}, "./canary": {"types@<=5.0": {"default": "./ts5.0/canary.d.ts"}, "types": {"default": "./canary.d.ts"}}, "./compiler-runtime": {"types": {"default": "./compiler-runtime.d.ts"}}, "./experimental": {"types@<=5.0": {"default": "./ts5.0/experimental.d.ts"}, "types": {"default": "./experimental.d.ts"}}, "./jsx-runtime": {"types@<=5.0": {"default": "./ts5.0/jsx-runtime.d.ts"}, "types": {"default": "./jsx-runtime.d.ts"}}, "./jsx-dev-runtime": {"types@<=5.0": {"default": "./ts5.0/jsx-dev-runtime.d.ts"}, "types": {"default": "./jsx-dev-runtime.d.ts"}}, "./package.json": "./package.json"}, "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react", "license": "MIT", "main": "", "name": "@types/react", "peerDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react"}, "scripts": {}, "typeScriptVersion": "5.1", "types": "index.d.ts", "typesPublisherContentHash": "955ad44f609574f1aa357f302a000fd1ff72040c97acd98002fc0f625c4bb5f3", "typesVersions": {"<=5.0": {"*": ["ts5.0/*"]}}, "version": "19.0.14"}