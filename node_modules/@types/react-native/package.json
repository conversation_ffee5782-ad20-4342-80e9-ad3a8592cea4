{"_from": "@types/react-native", "_id": "@types/react-native@0.73.0", "_inBundle": false, "_integrity": "sha512-6ZRPQrYM72qYKGWidEttRe6M5DZBEV5F+MHMHqd4TTYx0tfkcdrUFGdef6CCxY0jXU7wldvd/zA/b0A/kTeJmA==", "_location": "/@types/react-native", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "@types/react-native", "name": "@types/react-native", "escapedName": "@types%2freact-native", "scope": "@types", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/@types/react-native/-/react-native-0.73.0.tgz", "_shasum": "b316be230745779814caa533360262140b0f5984", "_spec": "@types/react-native", "_where": "/Users/<USER>/projects/SnapFold", "bundleDependencies": false, "dependencies": {"react-native": "*"}, "deprecated": "This is a stub types definition. react-native provides its own type definitions, so you do not need this installed.", "description": "Stub TypeScript definitions entry for react-native, which provides its own types definitions", "license": "MIT", "main": "", "name": "@types/react-native", "scripts": {}, "version": "0.73.0"}